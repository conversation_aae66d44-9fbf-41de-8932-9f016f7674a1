import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import SettingBody from '../../../setting/body/SettingBody';
import AutoSave from '../../../shared/autosave/AutoSave';
import Typography from '@/shared/components/common/Typography';
import AccessSwitch from '@/shared/components/common/AccessSwitch/AccessSwitch';

import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
// import icArrowDown from '@/assets/icons/layout/ic-arrow-down.svg'

import * as S from './CustomizeYourInbox.styles';
import Select from '@/shared/components/common/Select';
import { ReactSVG } from 'react-svg';

const CustomizeYourInbox = () => {
  const { t } = useTranslation('inboxSetting');

  const emailOptions = [
    { key: 'show-segments', labelKey: 'Show segments' },
    { key: 'show-channel', labelKey: 'Show channel' },
    { key: 'show-waiting-since-date', labelKey: 'Show waiting since date' },
    {
      key: 'show-feedback',
      labelKey: 'Ask users for feedback at the end of the articles',
    },
    { key: 'show-subject', labelKey: 'Show subject' },
    {
      key: 'show-notes-in-last-message',
      labelKey: 'Show notes in last message',
    },
  ];

  const [settings, setSettings] = useState<Record<string, any>>({
    'hide-send-confirm-popup': false,
    'show-segments': true,
    'show-channel': true,
    'show-waiting-since-date': true,
    'show-feedback': false,
    'show-subject': true,
    'show-notes-in-last-message': false,
    'email-preview': 'automatic',
  });

  const handleToggle = useCallback((key: string) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  }, []);

  const handleSelectChange = useCallback((value: string) => {
    setSettings((prev) => ({
      ...prev,
      'email-preview': value,
    }));
  }, []);

  return (
    <SettingBody title={t('inbox-menu.customize')} headerRight={<AutoSave />}>
      {/* General Section */}
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            General options
          </Typography>
        </S.BoxTitle>

        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            Hide the “Send confirm" popup
          </Typography>
          <AccessSwitch
            checked={settings['hide-send-confirm-popup']}
            onChange={() => handleToggle('hide-send-confirm-popup')}
            value="hide-send-confirm-popup"
          />
        </S.BoxSubTitle1>

        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            {t('Email preview')}
          </Typography>
          <Select

            isRequired
            value={settings['email-preview']}
            onChange={(value) =>
              setSettings((prev) => ({ ...prev, 'email-preview': value }))
            }
            options={[
              { value: 'automatic', label: 'Automatic' },
              { value: 'minimal', label: 'Minimal' },
              { value: 'detailed', label: 'Detailed' },
              { value: 'none', label: 'None' },
            ]}
            style={{ width: 300 }}
          />
        </S.BoxSubTitle1>
      </S.BoxSection>

      {/* Conversations List Section */}
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            Conversations list
          </Typography>
        </S.BoxTitle>

        {emailOptions.map(({ key, labelKey }) => (
          <S.BoxSubTitle1 key={key}>
            <Typography color={themeColors.neutral}>{t(labelKey)}</Typography>
            <AccessSwitch
              checked={settings[key]}
              onChange={() => handleToggle(key)}
              value={key}
            />
          </S.BoxSubTitle1>
        ))}
      </S.BoxSection>
    </SettingBody>
  );
};

export default CustomizeYourInbox;
