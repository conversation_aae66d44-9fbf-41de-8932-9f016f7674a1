import { ReactSVG } from 'react-svg';
import SettingBody from '../../../setting/body/SettingBody';
import icGlobal from '@/assets/icons/mail/ic-global.svg';

import * as S from './email-domain.styles';
import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import Typography from '@/shared/components/common/Typography';
import Input from '@/shared/components/common/Input';
import { useTranslation } from 'react-i18next';
import Button from '@/shared/components/common/Button';
import AutoSave from '../../../shared/autosave/AutoSave';

const EmailDomain = () => {
  const { t } = useTranslation('emailSetting');

  return (
    <SettingBody
      title={t('email-domain.title')}
      headerRight={<AutoSave/>
      }
    >
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('email-domain.section-title')}
          </Typography>
        </S.BoxTitle>
        <S.BoxWrapField>
          <S.FormItem
            name="domain"
            rules={[
              {
                required: true,
                message: '',
              },
              {
                type: 'text',
                message: '',
              },
            ]}
          >
            <Input
              prefix={icGlobal}
              label={t('email-domain.basic-domain-label')}
              isRequired
              placeholder={t('email-domain.basic-domain-placeholder')}
              type="text"
            />
            <S.Label>{t('email-domain.domain-suffix')}</S.Label>
          </S.FormItem>
          <S.FormItem
            name="subdomain"
            rules={[
              {
                required: true,
                message: '',
              },
              {
                type: 'text',
                message: '',
              },
            ]}
          >
            <Input
              prefix={icGlobal}
              label={t('email-domain.custom-domain-label')}
              isRequired
              placeholder={t('email-domain.custom-domain-placeholder')}
              type="text"
            />
          </S.FormItem>
        </S.BoxWrapField>
        <S.ButtonWrapper>
          <Button type="primary">
            {t('email-domain.verify-domain-setup')}
          </Button>
        </S.ButtonWrapper>
      </S.BoxSection>
    </SettingBody>
  );
};

export default EmailDomain;
