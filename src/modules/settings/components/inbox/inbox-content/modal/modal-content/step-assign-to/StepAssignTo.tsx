import Checkbox from '@/shared/components/common/Checkbox';
import React from 'react';
import styled from 'styled-components';
import avatar from '@/assets/images/settings/avatar.png';
import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';

interface User {
  id: number;
  name: string;
  avatar: string;
}

const users: User[] = [
  { id: 1, name: '<PERSON><PERSON><PERSON><PERSON>', avatar: avatar },
  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', avatar: avatar },
  { id: 3, name: '<PERSON><PERSON><PERSON><PERSON>', avatar: avatar },
  { id: 4, name: '<PERSON><PERSON><PERSON><PERSON>', avatar: avatar },
  { id: 5, name: '<PERSON><PERSON><PERSON><PERSON>', avatar: avatar },
  { id: 6, name: '<PERSON><PERSON><PERSON><PERSON>', avatar: avatar },
];

const Container = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
`;

const CheckboxCard = styled.div<{ checked: boolean }>`
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1 1 calc(50% - 12px);
  max-width: 50%;
  background-color: ${props => (props.checked ? '#DEE9E2' : 'white')};

  &:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
  }
`;

const Avatar = styled.img`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 12px;
`;

const UserName = styled.span`
  font-size: 14px;
  color: ${themeColors?.neutral};
  font-weight: ${fontWeight?.semiBold};
`;

const CheckboxItem: React.FC<{ user: User; defaultChecked?: boolean }> = ({ user, defaultChecked }) => {
  const [checked, setChecked] = React.useState(!!defaultChecked);

  const handleChange = (e: any) => {
    setChecked(e.target.checked);
  };

  return (
    <CheckboxCard checked={checked} onClick={() => setChecked(prev => !prev)}>
      <Checkbox
        checked={checked}
        onChange={handleChange}
      />
      <Avatar src={user.avatar} alt={user.name} />
      <UserName>{user.name}</UserName>
    </CheckboxCard>
  );
};

const StepAssignTo = () => {
  return (
    <Container>
      {users.map((user, index) => (
        <CheckboxItem
          key={user.id}
          user={user}
          defaultChecked={index === 0}
        />
      ))}
    </Container>
  );
};

export default StepAssignTo;
