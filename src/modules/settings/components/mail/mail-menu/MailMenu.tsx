import React from 'react';
import { useTranslation } from 'react-i18next';
import SettingMenu from '../../setting/menu/SettingMenu';
import { MailMenusEnums } from '@/modules/settings/helpers/enums/mail';
import icSetting from '@/assets/icons/mail/ic-setting.svg'
import icLocate from '@/assets/icons/mail/ic-locate.svg'
import icDelivery from '@/assets/icons/mail/ic-delivery.svg'


const MailMenu: React.FC = () => {
  const { t } = useTranslation('emailSetting');

  const menuItems = [
    {
      key: MailMenusEnums.EMAIL_BEHAVIOR,
      label: t('mail-menu.email-behavior'),
      icon: icSetting,
    },
    {
      key: MailMenusEnums.EMAIL_DOMAIN,
      label: t('mail-menu.email-domain'),
      icon: icLocate,
    },
    {
      key: MailMenusEnums.EMAIL_DELIVERY,
      label: t('mail-menu.email-delivery'),
      icon: icDelivery,
    },
  ];

  return (
    <SettingMenu
      title={t('mail-menu.mail-setting')}
      menuItems={menuItems}
      basePath="/setting/email"
    />
  );
};

export default MailMenu;
