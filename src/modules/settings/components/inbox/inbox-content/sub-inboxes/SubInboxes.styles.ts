import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import { Form } from 'antd';
import styled, { css } from 'styled-components';

export const LabelActive = styled.div`
  display: flex;
  align-items: center;
  color: ${themeColors?.primary};
  gap: 4px;
  font-style: italic;
`;


export const BoxSection = styled.div`
  background-color: ${themeColors?.newtralLightest};
  border: 1px solid ${themeColors?.newtral};
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 12px;
`;

export const BoxTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${themeColors?.newtral};
  padding-bottom: 16px;
  margin-bottom: 16px;
`;

export const BoxSubTitle = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  padding-bottom: 16px;
`;

export const BoxSubTitle1 = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 24px;
  button{
    width:fit-content;
  }
`;