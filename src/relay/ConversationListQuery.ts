import { graphql } from 'react-relay';

export const conversationListQuery = graphql`
  query ConversationListQuery($first: Float, $after: String, $last: Float, $before: String) {
    conversations(first: $first, after: $after, last: $last, before: $before) {
      edges {
        cursor
        node {
          id
          contact {
            avatar
            name
            isOnline
          }
          status
          subject
          metadata
          createdAt
          updatedAt
          lastActivityAt
          closedAt
          unreadCount
          assignedTo {
            id
          }
          latestMessage {
            content
          }
        }
      }
    }
  }
`;
