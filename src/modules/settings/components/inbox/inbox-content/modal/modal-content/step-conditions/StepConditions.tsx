import React, { useState } from 'react';
import Typography from '@/shared/components/common/Typography';
import { Image } from 'antd';

import * as S from './StepConditions.styles';
import icSimpleCondition from '@/assets/icons/inbox/ic-simple-condition.svg';
import icAdvancedCondition from '@/assets/icons/inbox/ic-advanced-condition.svg';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import themeColors from '@/shared/styles/themes/default/colors';
import ConditionFilter from './condition-filter/ConditionFilter';


const StepConditions = () => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  if (showAdvanced) {
    return <ConditionFilter />;
  }

  return (
    <S.Container>
      <S.ChildSection>
        <Image src={icSimpleCondition} preview={false} />
        <S.BoxContent>
          <Typography fontWeight={fontWeight?.semiBold}>
            Simple Condition
          </Typography>
          <Typography color={themeColors?.newtralDark}>
            Assign to the specified operators in a direct way.
          </Typography>
        </S.BoxContent>
      </S.ChildSection>

      <S.ChildSection onClick={() => setShowAdvanced(true)}>
        <Image src={icAdvancedCondition} preview={false} />
        <S.BoxContent>
          <Typography fontWeight={fontWeight?.semiBold}>
            Advanced Condition
          </Typography>
          <Typography color={themeColors?.newtralDark}>
            Assign based on segments, user data, user locale, and more.
          </Typography>
        </S.BoxContent>
      </S.ChildSection>
    </S.Container>
  );
};

export default StepConditions;
