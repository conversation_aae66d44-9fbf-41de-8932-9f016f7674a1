import React from 'react';
import SettingBody from '../../../setting/body/SettingBody';
import { useTranslation } from 'react-i18next';
import * as S from './SetupKnowledgeBase.styles';
import Typography from '@/shared/components/common/Typography';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import themeColors from '@/shared/styles/themes/default/colors';
import Input from '@/shared/components/common/Input';
import icGlobal from '@/assets/icons/mail/ic-global.svg';
import AutoSave from '../../../shared/autosave/AutoSave';

const SetupKnowledgeBase = () => {
  const { t } = useTranslation('knowledgeBaseSetting');
  return (
    <SettingBody
      title={t('knowledge-base.setup-knowledge-base')}
      headerRight={<AutoSave/>}
    >
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('setup-knowledge-base.general-options')}
          </Typography>
        </S.BoxTitle>
        <S.BoxWrapField>
          <S.FormItem
            name="domain"
            rules={[
              {
                required: true,
                message: '',
              },
              {
                type: 'text',
                message: '',
              },
            ]}
          >
            <Input
              prefix={icGlobal}
              label={t('setup-knowledge-base.basic-domain-label')}
              isRequired
              placeholder={t('setup-knowledge-base.basic-domain-placeholder')}
              type="text"
            />
            <S.Label>{t('setup-knowledge-base.domain-suffix')}</S.Label>
          </S.FormItem>
          <S.FormItem
            name="subdomain"
            rules={[
              {
                required: true,
                message: '',
              },
              {
                type: 'text',
                message: '',
              },
            ]}
          >
            <Input
              prefix={icGlobal}
              label={t('setup-knowledge-base.custom-domain-label')}
              isRequired
              placeholder={t('setup-knowledge-base.custom-domain-placeholder')}
              type="text"
            />
          </S.FormItem>
        </S.BoxWrapField>
      </S.BoxSection>
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            Custom domain setup instruction
          </Typography>
        </S.BoxTitle>
        <S.BoxSubTitle>
          <S.LabelNumber>1</S.LabelNumber>
          <Typography> Login to your DNS manager for help.t2bo.com</Typography>
        </S.BoxSubTitle>
        <S.BoxSubTitle>
          <S.LabelNumber>2</S.LabelNumber>
          <Typography>Add TXT DNS entry for</Typography>
          <S.LabelText>
            onlychat-website-id=a048e736-b92c-49a8-b2b2-970b429ff6d3
          </S.LabelText>
          <Typography> .t2bo.com with value </Typography>
          <S.LabelText>
            onlychat-website-id=a048e736-b92c-49a8-b2b2-970b429ff6d3
          </S.LabelText>
        </S.BoxSubTitle>

        <S.BoxSubTitle>
          <S.LabelNumber>3</S.LabelNumber>
          <Typography>Add CNAME DNS entry for</Typography>
          <S.LabelText>help</S.LabelText>
          <Typography> .t2bo.com with value </Typography>
          <S.LabelText>custom.onlychat.help</S.LabelText>
        </S.BoxSubTitle>
        <S.BoxSubTitle>
          <S.LabelNumber>4</S.LabelNumber>
          <Typography>
            {' '}
            Wait for DNS to propagate (this way take a few hours). Use the
            Verify domain setup button below
          </Typography>
        </S.BoxSubTitle>
      </S.BoxSection>
    </SettingBody>
  );
};

export default SetupKnowledgeBase;
