import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Image } from 'antd';

import Modal from '@/shared/components/common/Modal';
import Button from '@/shared/components/common/Button';
import Typography from '@/shared/components/common/Typography';

import * as S from './RoutingRuleModal.styles';

import icProgress from '@/assets/icons/knowledge-base/ic-progess-1.svg';
import icProgressActive from '@/assets/icons/knowledge-base/ic-progress-active.svg';
import icSuccess from '@/assets/icons/knowledge-base/ic-success.svg';

import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import StepName from './modal-content/step-name/StepName';
import StepConditions from './modal-content/step-conditions/StepConditions';
import StepAssignTo from './modal-content/step-assign-to/StepAssignTo';

interface RoutingRuleModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RoutingRuleModal: React.FC<RoutingRuleModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation('knowledgeBase');
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    { label: "NAME" },
    { label: "CONDITIONS"},
    { label: "ASSIGN TO" },
  ];

  const handleSave = () => {
    // TODO: Implement save logic here
    onClose();
  };

  const handleContinue = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep((prev) => prev + 1);
    } else {
      handleSave();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('article-menu.getting-started-knowledge.title')}
      width={700}
      footer={
        <>
          <Button onClick={onClose}>{t('article-menu.getting-started-knowledge.cancel')}</Button>
          <Button type="primary" onClick={handleContinue}>
            {activeStep < steps.length - 1
              ? t('article-menu.getting-started-knowledge.continue')
              : "Save routing rule"}
          </Button>
        </>
      }
    >
      <S.ModalStepsContainer>
        <S.Diver />
        {steps.map((step, index) => {
          const isActive = index === activeStep;

          return (
            <S.StepItem key={index} active={isActive}>
              <Image
                src={index < activeStep ? icSuccess : isActive ? icProgressActive : icProgress}
                width={20}
                height={20}
                preview={false}
              />
              <Typography
                fontWeight={isActive ? fontWeight.semiBold : fontWeight.medium}
                color={isActive ? themeColors.primary : themeColors.newtralLight}
                margin="4px 0"
              >
                {step.label}
              </Typography>
            </S.StepItem>
          );
        })}
      </S.ModalStepsContainer>

      <S.ModalStepContent>
        {activeStep === 0 && <StepName/>}
        {activeStep === 1 && <StepConditions/>}
        {activeStep === 2 && <StepAssignTo/>}
      </S.ModalStepContent>
    </Modal>
  );
};

export default RoutingRuleModal;
