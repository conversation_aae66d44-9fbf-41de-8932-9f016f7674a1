import { TFunction } from 'i18next';

import { postRequest } from '@/core/services/requests';
import { patchRequest } from '@/core/services/requests/patchRequest';
import {
  actionSetGlobalLoading,
  actionCreateWorkspace,
} from '@/modules/auth/store/features/auth';
import { eventBus } from '@/core/event-bus';
import { EVENTBUS_WORKSPACE_CHANGED } from '@/core/settings/constants';
import { store } from '@/core/store';

const prefixAuth: string = '';

const endpointAuth = {
  WORKSPACE: `${prefixAuth}/workspaces`,
  CURRENT_WORKSPACE: `${prefixAuth}/users/current-workspace`,
};

// Decode Relay Global ID to raw ID
const decodeGlobalId = (globalId: string): string => {
  try {
    const decoded = atob(globalId);
    const parts = decoded.split(':');
    return parts[1] || globalId; // Return raw ID or fallback to original
  } catch {
    return globalId; // If decode fails, return original
  }
};

const handleCreateWorkspaceApi = async (
  values: any,
  t: TFunction,
  handleOpenModalCreateWorkspace: () => void,
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  dispatch: React.Dispatch<any>,
) => {
  await postRequest(endpointAuth?.WORKSPACE, {
    data: values,
    messageSuccess: t('create-workspace.create-workspace-success'),
  })
    .then((res) => {
      handleOpenModalCreateWorkspace();
      dispatch(actionCreateWorkspace(res));
    })
    .catch((err) => err)
    .finally(() => setIsLoading((prev) => !prev));
};

const handleSwitchWorkspaceApi = async (
  workspaceId: string,
  t: TFunction,
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  onSuccess?: (token: string) => void,
) => {
  setIsLoading(true);
  store.dispatch(actionSetGlobalLoading(true));
  try {
    // Decode Global ID to raw ID for backend API
    const rawWorkspaceId = decodeGlobalId(workspaceId);
    
    const response = await patchRequest(endpointAuth?.CURRENT_WORKSPACE, {
      data: { workspaceId: rawWorkspaceId },
      messageSuccess: t('create-workspace.switch-success'),
    });

    if (response?.token) {
      onSuccess?.(response.token);
      eventBus.emit(EVENTBUS_WORKSPACE_CHANGED as any);
    }
  } catch (error) {
    console.error('Failed to switch workspace:', error);
  } finally {
    setIsLoading(false);
    store.dispatch(actionSetGlobalLoading(false));
  }
};

export { handleCreateWorkspaceApi, handleSwitchWorkspaceApi };
