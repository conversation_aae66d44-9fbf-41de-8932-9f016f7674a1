import { useState } from 'react';
import SettingBody from '../../../setting/body/SettingBody';
import { useTranslation } from 'react-i18next';
import * as S from './AdvancedSetting.styles';
import Typography from '@/shared/components/common/Typography';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import themeColors from '@/shared/styles/themes/default/colors';
import Button from '@/shared/components/common/Button';
import AutoSave from '../../../shared/autosave/AutoSave';
const AdvancedSetting = () => {
  const { t } = useTranslation('knowledgeBaseSetting');

  return (
    <SettingBody
      title={t('knowledge-base.advanced-settings')}
      headerRight={<AutoSave/>}
      
    >
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('setup-knowledge-base.custom-options')}
          </Typography>
        </S.BoxTitle>
        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            Show an alert when status reports dead (if ZestChat Status is configured)
          </Typography>
          <Button type='primary'>Add A Page Redirection</Button>
        </S.BoxSubTitle1>
        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
           Show an alert when status reports dead (if ZestChat Status is configured)
          </Typography>
          <Button type='primary'>Configure The Knowledge Base Password</Button>
        </S.BoxSubTitle1>
      </S.BoxSection>
    </SettingBody>
  );
};

export default AdvancedSetting;
