import SettingBody from '@/modules/settings/components/setting/body/SettingBody';

import visaLogo from '@/assets/icons/setting/ic-visa.svg';
import mastercardLogo from '@/assets/icons/setting/ic-martercard.svg';

import * as S from './workspace-billing-setting.style';
import icValid from '@/assets/icons/setting/ic-tick-circle.svg';
import Typography from '@/shared/components/common/Typography';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import AutoSave from '../../../shared/autosave/AutoSave';

const mockCards = [
  {
    type: 'mastercard',
    owner: 'LE BAO CHAU',
    address: 'Da Nang',
    vat: '-',
   
    cardNumber: '****************',
    exp: '12/29',
    valid: true,
  },
  {
    type: 'visa',
    owner: 'Tran <PERSON>',
    address: 'Da Nang',
    vat: '-',
    cardNumber: '****************',
    exp: '01/28',
    valid: true,
  },
   {
    type: 'visa',
    owner: '<PERSON><PERSON>',
    address: '<PERSON>',
    vat: '-',
    cardNumber: '****************',
    exp: '01/28',
    valid: true,
  },
];

const WorkspceBillingSetting = () => {
  return (
    <SettingBody
      title="Billing settings"
      headerRight={<AutoSave/>
      }
    >
      <S.WrapperBody>
        {mockCards.map((card, idx) => (
          <S.CardItem key={idx}>
            <S.CardLeft>
              <S.CardLeftHeader>
                <Typography fontWeight={fontWeight?.semiBold}>Card</Typography>
                <img
                  src={card.type === 'visa' ? visaLogo : mastercardLogo}
                  alt={card.type}
                  style={{ width: 53 }}
                />
              </S.CardLeftHeader>

              <S.CardNumber>
                <div style={{ fontSize: '14px', letterSpacing: '4px' }}>
                  •••• •••• •••• {card.cardNumber?.slice(-4)}
                </div>
              </S.CardNumber>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: 13,
                }}
              >
                <div>
                  <S.CardInfoLabel>CARD HOLDER NAME</S.CardInfoLabel>
                  <S.CardInfoValue>{card.owner}</S.CardInfoValue>
                </div>

                <div>
                  <S.CardInfoLabel>EXPIRY DATE</S.CardInfoLabel>
                  <S.CardInfoValue>{card.exp}</S.CardInfoValue>
                </div>

                <div>
                  <S.CardValid>
                    <img src={icValid} alt="valid" style={{ width: 16 }} />
                    <span>Valid</span>
                  </S.CardValid>
                </div>
              </div>
            </S.CardLeft>
          </S.CardItem>
        ))}
      </S.WrapperBody>
    </SettingBody>
  );
};

export default WorkspceBillingSetting;
