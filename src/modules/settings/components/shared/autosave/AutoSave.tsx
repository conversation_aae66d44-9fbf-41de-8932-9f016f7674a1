import { ReactSVG } from 'react-svg';
import icCheck from '@/assets/icons/setting/ic-check.svg';
import * as S from './AutoSave.styles';
import { useTranslation } from 'react-i18next';

const AutoSave = () => {
  const { t } = useTranslation('knowledgeBaseSetting');
  return (
    <S.LabelActive>
      <ReactSVG src={icCheck} />
      {t('setup-knowledge-base.auto-saved')}
    </S.LabelActive>
  );
};

export default AutoSave;
