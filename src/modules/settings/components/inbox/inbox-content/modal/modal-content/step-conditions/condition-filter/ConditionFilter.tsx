import React, { useState } from 'react';
import { Form, Image, Space } from 'antd';

import Typography from '@/shared/components/common/Typography';
import fontWeight from '@/shared/styles/themes/default/fontWeight';

import icEdit from '@/assets/icons/contact/ic-edit-primary.svg';
import icClose from '@/assets/icons/contact/ic-close-circle.svg';
import arrowDown from '@/assets/icons/common/ic-arrow-down.svg';
import search from '@/assets/icons/common/ic-search.svg';

import * as S from './ConditionFilter.styles';
import Select from '@/shared/components/common/Select';
import { useTranslation } from 'react-i18next';
import Input from '@/shared/components/common/Input';
import Button from '@/shared/components/common/Button';

const ConditionFilter = () => {
  const [form] = Form.useForm();
  const { t } = useTranslation('contacts');

  const operatorOptions = [
    { label: 'Equal to', value: 'equal' },
    { label: 'Contains', value: 'contains' },
    { label: 'Starts with', value: 'startsWith' },
    { label: 'Ends with', value: 'endsWith' },
    { label: 'Is empty', value: 'isEmpty' },
    { label: 'Is not empty', value: 'isNotEmpty' },
  ];

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCriterion, setSelectedCriterion] = useState<string>('Select a crioterion');

  const criterionOptions = ['User email', 'User phone', 'User IP address', 'Conversation state', 'User availability', 'Creation date', 'Update date' ];

  const filteredOptions = criterionOptions.filter((opt) =>
    opt.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <div>
      <S.FilterSection>
        <S.FilterWrap>
          <S.Title>
            <Typography>User email</Typography>
            <Typography>
              <Image preview={false} src={icClose} />
            </Typography>
          </S.Title>

          {/* Operator select */}
          <Form.Item
            name="filter"
            rules={[
              {
                required: true,
                message: t('start-for-free.please-company-size-placeholder'),
              },
            ]}
          >
            <Select
              options={operatorOptions}
              defaultValue={operatorOptions[0]?.value}
              isRequired
              allowClear
              showSearch
            />
          </Form.Item>

          {/* Input value */}
          <S.FormItem
            name="name"
            rules={[
              {
                required: true,
                message: '',
              },
              {
                type: 'string',
                message: t('name-invalid'),
              },
            ]}
          >
            <Input
              isRequired
              type="text"
            />
          </S.FormItem>
        </S.FilterWrap>
      </S.FilterSection>

      <S.SearchIconDropdown>
        <S.Label>{t('add-filter.and')}</S.Label>
      </S.SearchIconDropdown>

      <S.FilterSection>
        {/* Criterion dropdown only for selection */}
        <S.MainFilterDropdownWrapper>
          <S.ButtonModalDropdown onClick={() => setIsDropdownOpen((prev) => !prev)}>
            {selectedCriterion}
            <Image src={arrowDown} preview={false} />
          </S.ButtonModalDropdown>

          {!isDropdownOpen && (
            <S.ModalDropdownBorder>
              <S.BoxDropDown>
                <S.SearchWrapper>
                  <S.SearchIconDropdown>
                    <Image src={search} alt="Search icon" preview={false} style={{ width: 16 }} />
                  </S.SearchIconDropdown>
                  <S.SearchInputDropdown
                    placeholder="Select a crioterion"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </S.SearchWrapper>

                <S.ListOptions>
                  {filteredOptions.map((option) => (
                  <S.DropdownBorder
                    key={option}
                    onClick={() => {
                      setSelectedCriterion(option);
                      setIsDropdownOpen(false);
                      setSearchTerm('');
                    }}
                  >
                    {option}
                  </S.DropdownBorder>
                ))}
                </S.ListOptions>
              </S.BoxDropDown>
            </S.ModalDropdownBorder>
          )}
        </S.MainFilterDropdownWrapper>
      </S.FilterSection>

       <S.SearchIconDropdown>
        <Button>{t('add-filter.add-another-condition')}</Button>
      </S.SearchIconDropdown>

    </div>
  );
};

export default ConditionFilter;
