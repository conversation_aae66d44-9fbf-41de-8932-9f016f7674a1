import { styled } from 'styled-components';

export const ColorPickerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

export const ColorLabel = styled.label`
  font-size: 14px;
  font-weight: 400;
  color: #333;
`;

export const ColorPickerWrapper = styled.div`
  display: flex;
  align-items: center;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  background: #fff;
  height: 44px;
  width: 180px;
  position: relative;
  overflow: hidden;
  
  &:hover {
    border-color: #9ca3af;
  }
  
  &:focus-within {
    border-color: #6b7280;
  }
`;

export const ColorSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  cursor: pointer;
`;

export const ColorDisplay = styled.div<{ color: string }>`
  width: 28px;
  height: 28px;
  background-color: ${props => props.color};
  border-radius: 6px;
  flex-shrink: 0;
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 0.9;
  }
`;

export const Divider = styled.div`
  width: 1px;
  height: 28px;
  background-color: #e5e7eb;
  flex-shrink: 0;
`;

export const ColorInput = styled.input`
  position: absolute;
  opacity: 0;
  width: 100px;
  height: 100%;
  right: 0;
  top: 0;
  cursor: pointer;
  border: none;
  background: none;
`;

export const HexDisplay = styled.div`
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  font-family: 'SF Pro Text', -apple-system, system-ui, sans-serif;
  font-weight: 400;
  flex: 1;
  height: 100%;
  padding: 0 16px;
  color: #374151;
  display: flex;
  align-items: center;
  cursor: pointer;
  min-width: 0;
`;

export const DropdownIcon = styled.div`
  width: 20px;
  height: 20px;
  background: url('/src/assets/icons/setting/ic-arrow-up.svg') no-repeat center center;
  background-size: 14px 14px;
  transform: rotate(180deg);
  opacity: 0.5;
  flex-shrink: 0;
  filter: brightness(0) saturate(100%) invert(60%);
`; 