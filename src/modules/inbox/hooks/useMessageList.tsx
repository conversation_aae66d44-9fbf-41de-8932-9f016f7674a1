import { useEffect, useCallback } from 'react';
import {
  commitL<PERSON>alUpdate,
  ConnectionHandler,
  useLazyLoadQuery,
  usePaginationFragment,
} from 'react-relay';
import { RecordSourceSelectorProxy } from 'relay-runtime';
import { messageListFragment } from '@/relay/MessageFragment';
import { conversationMessagesQuery } from '@/relay/ConversationMessagesQuery';
import { Message } from '../interfaces/inbox';
import { eventBus } from '@/core/event-bus';
import { EVENTBUS_INBOX_MESSAGE } from '@/core/settings/constants';
import {
  InboxMessageType,
  InboxMessageStatus,
  InboxSender,
} from '@/modules/settings/helpers/enums/inbox.enums';
import type { ConversationMessagesQuery } from '@/relay/__generated__/ConversationMessagesQuery.graphql';
import type { MessageFragment_query$key } from '@/relay/__generated__/MessageFragment_query.graphql';
import environment from '@/relay/RelayEnvironment';

interface UseMessageListProps {
  conversationId: string | null;
}

const MESSAGE_LIMIT = 20;

function isValidMessage(msg: Message): boolean {
  if (!msg || !msg.type) return false;
  if (
    msg.type === InboxMessageType.Text ||
    msg.type === InboxMessageType.Note
  ) {
    return !!msg.content && msg.content.trim() !== '';
  }
  if (msg.type === InboxMessageType.Image) {
    return !!msg.metadata?.fileUrl;
  }

  return false;
}

// Decode Relay Global ID to raw ID
const decodeGlobalId = (globalId: string): string => {
  try {
    const decoded = atob(globalId);
    const parts = decoded.split(':');
    return parts[1] || globalId;
  } catch {
    return globalId;
  }
};

const mapSender = (sender: string): InboxSender =>
  sender === 'GUEST' ? InboxSender.Guest : InboxSender.Agent;

const mapType = (type: string): InboxMessageType => {
  switch (type) {
    case 'IMAGE':
      return InboxMessageType.Image;
    case 'NOTE':
      return InboxMessageType.Note;
    default:
      return InboxMessageType.Text;
  }
};

const mapStatus = (status: string): InboxMessageStatus => {
  switch (status) {
    case 'FAILED':
      return InboxMessageStatus.Failed;
    case 'SENDING':
      return InboxMessageStatus.Sending;
    default:
      return InboxMessageStatus.Sent;
  }
};

export function useMessageList({ conversationId }: UseMessageListProps) {
  // Return empty state if no conversationId
  if (!conversationId) {
    return {
      messages: [],
      loading: false,
      loadingMore: false,
      error: null,
      hasNextPage: false,
      loadMore: () => {},
      addMessage: () => {},
      updateMessage: () => {},
      removeMessage: () => {},
    };
  }

  const rawConversationId = decodeGlobalId(conversationId);

  // Load initial messages using Relay
  const queryData = useLazyLoadQuery<ConversationMessagesQuery>(
    conversationMessagesQuery,
    {
      conversationId: rawConversationId,
      first: MESSAGE_LIMIT,
      after: null,
    },
  );

  // Use pagination fragment to handle loading more messages
  const { data, loadNext, hasNext, isLoadingNext, refetch } =
    usePaginationFragment<ConversationMessagesQuery, MessageFragment_query$key>(
      messageListFragment,
      queryData,
    );

  // Transform Relay data to our Message interface
  const relayMessages: Message[] =
    data?.messages?.edges
      ?.map((edge: any) => {
        const node = edge.node;
        return {
          id: node.id,
          content: node.content,
          sender: mapSender(node.sender),
          createdAt: node.createdAt,
          updatedAt: node.updatedAt ?? node.createdAt,
          metadata: node.metadata ?? undefined,
          user: node.user
            ? {
                id: node.user.id,
                firstName: node.user.firstName,
                lastName: node.user.lastName,
                avatar: node.user.avatar,
              }
            : null,
          type: mapType(node.type),
          status: mapStatus(node.status),
        };
      })
      .filter(isValidMessage) || [];

  // Load more messages function
  const loadMore = useCallback(() => {
    if (hasNext && !isLoadingNext) {
      loadNext(MESSAGE_LIMIT);
    }
  }, [hasNext, isLoadingNext, loadNext]);

  // Listen for new messages via event bus
  useEffect(() => {
    const handleNewMessage = (msg: Message) => {
      if (!isValidMessage(msg)) return;
      // Refetch to get latest data from server
      refetch({
        conversationId: rawConversationId,
        first: MESSAGE_LIMIT,
        after: null,
      });
    };

    eventBus.on(EVENTBUS_INBOX_MESSAGE, handleNewMessage);
    return () => {
      eventBus.off(EVENTBUS_INBOX_MESSAGE, handleNewMessage);
    };
  }, [rawConversationId, refetch]);

  // Function to add message to Relay store
  const addMessageToStore = useCallback(
    (msg: Message, conversationId: string) => {
      commitLocalUpdate(environment, (store: RecordSourceSelectorProxy) => {
        // Get the root query record
        const root = store.getRoot();

        // Get the connection using the connection key from the fragment
        const connection = ConnectionHandler.getConnection(
          root,
          'MessageFragment_messages',
          { conversationId },
        );

        if (!connection) {
          console.warn('Connection not found for adding message', {
            conversationId,
            rootDataID: root.getDataID(),
          });
          return;
        }

        console.log('Connection found, adding message:', msg.id);

        // Create a new message record
        const messageRecord = store.create(msg.id, 'Message');
        messageRecord.setValue(msg.id, 'id');
        messageRecord.setValue(msg.content, 'content');
        messageRecord.setValue(msg.sender, 'sender');
        messageRecord.setValue(msg.createdAt, 'createdAt');
        messageRecord.setValue(msg.updatedAt, 'updatedAt');
        messageRecord.setValue(msg.type, 'type');
        messageRecord.setValue(msg.status, 'status');
        messageRecord.setValue(conversationId, 'conversationId');

        if (msg.metadata) {
          messageRecord.setValue(JSON.stringify(msg.metadata), 'metadata');
        }

        if (msg.user) {
          const userRecord = store.create(`user_${msg.user.id}`, 'User');
          userRecord.setValue(msg.user.id, 'id');
          userRecord.setValue(msg.user.firstName, 'firstName');
          userRecord.setValue(msg.user.lastName, 'lastName');
          userRecord.setValue(msg.user.avatar, 'avatar');
          messageRecord.setLinkedRecord(userRecord, 'user');
        }

        // Create an edge for the message
        const edge = ConnectionHandler.createEdge(
          store,
          connection,
          messageRecord,
          'MessageTypeEdge',
        );

        // Insert the edge at the beginning of the connection (newest first)
        ConnectionHandler.insertEdgeBefore(connection, edge);
      });
    },
    [],
  );

  // Add optimistic message (shows immediately in UI)
  const addMessage = useCallback(
    (msg: Message) => {
      if (!isValidMessage(msg)) return;
      addMessageToStore(msg, rawConversationId);
    },
    [addMessageToStore, rawConversationId],
  );

  // Update message
  const updateMessage = useCallback(
    (messageId: string, updates: Partial<Message>) => {
      console.log(messageId, updates);
    },
    [],
  );

  // Remove message
  const removeMessage = useCallback((messageId: string) => {
    console.log(messageId);
  }, []);

  return {
    messages: relayMessages,
    loading: false,
    loadingMore: isLoadingNext,
    error: null,
    hasNextPage: hasNext,
    loadMore,
    addMessage,
    updateMessage,
    removeMessage,
    createTestMessage, // For testing purposes
  };
}
