import React from 'react';
import { useTranslation } from 'react-i18next';
import SettingMenu from '../../setting/menu/SettingMenu';
import { KNOWLEDGE_BASE_MENUS_ENUMS } from '@/modules/settings/helpers/enums/knowledgebase';
import icPaper from '@/assets/icons/knowledge-base/ic-paper.svg'
import icCustomize from '@/assets/icons/knowledge-base/ic-customize.svg'
import icAdvanced from '@/assets/icons/knowledge-base/ic-advanced-setting.svg'
const KnowledgeBaseMenu: React.FC = () => {
  const { t } = useTranslation('knowledgeBaseSetting');

  const knowledgeBaseMenus = [
    {
      key: KNOWLEDGE_BASE_MENUS_ENUMS.SETUP_KNOWLEDGE_BASE,
      label: t('knowledge-base.setup-knowledge-base'),
      icon: icPaper
    },
    {
      key: KNOWLEDGE_BASE_MENUS_ENUMS.CUSTOMIZE_KNOWLEDGE_BASE,
      label: t('knowledge-base.customize-knowledge-base'),
      icon: icCustomize
    },
    {
      key: KN<PERSON>LEDGE_BASE_MENUS_ENUMS.ADVANCED_SETTINGS,
      label: t('knowledge-base.advanced-settings'),
      icon: icAdvanced
    }
  ];

  return (
    <SettingMenu
      title={t('knowledge-base.menu-title')}
      menuItems={knowledgeBaseMenus}
      basePath="/setting/knowledge-base"
    />
  );
};

export default KnowledgeBaseMenu;
