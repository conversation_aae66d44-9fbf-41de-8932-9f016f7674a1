import * as S from './workspace-team-transparency-log.styles';

import SettingBody from '../../../setting/body/SettingBody';
import { ReactSVG } from 'react-svg';
import icCheck from '@/assets/icons/setting/ic-check.svg';
import icDate from '@/assets/icons/billing/ic-date-ip.svg';
import icAction from '@/assets/icons/workspace/ic-check.svg';
import icOperation from '@/assets/icons/workspace/ic-user-circle.svg';
import icTime from '@/assets/icons/workspace/ic-time.svg';
import icFlag from '@/assets/icons/workspace/ic-flag.svg';
import icLocal from '@/assets/icons/workspace/ic-local.svg';
import bgAvatar from '@/assets/images/settings/avatar.png';

import Typography from '@/shared/components/common/Typography';
import Button from '@/shared/components/common/Button';
import { useTranslation } from 'react-i18next';
import fontWeight from '@/shared/styles/themes/default/fontWeight';

import { DatePicker, Image, Select, Table } from 'antd';
import dayjs from 'dayjs';
import AccessSwitch from '@/shared/components/common/AccessSwitch/AccessSwitch';
import { useState } from 'react';
import AutoSave from '../../../shared/autosave/AutoSave';

const { RangePicker } = DatePicker;
const { Option } = Select;

const WorkspaceTeamTransparencyLog = () => {
  const { t } = useTranslation('settingWorkspace');
  const [isEnabled, setIsEnabled] = useState(false);

  const dataSource = [
    {
      key: '1',
      action: 'Login',
      operation: (
        <S.Userbox>
          <Image src={bgAvatar} preview={false} />
          ChauLB
        </S.Userbox>
      ),
      timeAgo: '5 minutes ago',
      ipAddress: (
        <S.IpBox>
          <Image src={icFlag} preview={false} />
          ***********
        </S.IpBox>
      ),
    },
    {
      key: '2',
      action: 'Edit settings',
      operation: (
        <S.Userbox>
          <Image src={bgAvatar} preview={false} />
          ChauLB
        </S.Userbox>
      ),
      timeAgo: '2 hours ago',
      ipAddress: (
        <S.IpBox>
          <Image src={icFlag} preview={false} />
          ***********
        </S.IpBox>
      ),
    },
    {
      key: '3',
      action: 'Remove member',
      operation: (
        <S.Userbox>
          <Image src={bgAvatar} preview={false} />
          ChauLB
        </S.Userbox>
      ),
      timeAgo: '1 day ago',
      ipAddress: (
        <S.IpBox>
          <Image src={icFlag} preview={false} />
          ***********
        </S.IpBox>
      ),
    },
    {
      key: '4',
      action: 'Remove member',
      operation: (
        <S.Userbox>
          <Image src={bgAvatar} preview={false} />
          ChauLB
        </S.Userbox>
      ),
      timeAgo: '1 day ago',
      ipAddress: (
        <S.IpBox>
          <Image src={icFlag} preview={false} />
          ***********
        </S.IpBox>
      ),
    },
  ];

  const columns = [
    {
      title: (
        <S.TitleBox>
          <ReactSVG src={icAction} />
          Action
        </S.TitleBox>
      ),
      dataIndex: 'action',
      key: 'action',
    },
    {
      title: (
        <S.TitleBox>
          <ReactSVG src={icOperation} />
          Operation
        </S.TitleBox>
      ),
      dataIndex: 'operation',
      key: 'operation',
    },
    {
      title: (
        <S.TitleBox>
          <ReactSVG src={icTime} />
          Time ago
        </S.TitleBox>
      ),
      dataIndex: 'timeAgo',
      key: 'timeAgo',
    },
    {
      title: (
        <S.TitleBox>
          <ReactSVG src={icLocal} />
          IP Address
        </S.TitleBox>
      ),
      dataIndex: 'ipAddress',
      key: 'ipAddress',
    },
  ];

  return (
    <SettingBody
      title={t('workspace-menus.team-transparency-log')}
      headerRight={<AutoSave/>
      }
    >
      <S.PlansWarning>
        <S.WraperSection>
          <Typography>{t('workspace-menus.please-install')}</Typography>
        </S.WraperSection>
        <Button type="primary">{t('workspace-menus.install-plugin')}</Button>
      </S.PlansWarning>

      <S.WrapperBody>
        <S.WraperTitle>
          <Typography fontWeight={fontWeight?.semiBold} variant="h5">
            Collect transparency logs from all team members
          </Typography>

          <AccessSwitch
            checked={isEnabled}
            onChange={setIsEnabled}
            value="access-control"
          />
        </S.WraperTitle>

        <S.FilterBar>
          <S.FilterItem>
            <S.DatePickerWrapper>
              <ReactSVG src={icDate} />
              <RangePicker
                format="DD/MM/YYYY"
                placeholder={[
                  t('workspace-menus.from'),
                  t('workspace-menus.to'),
                ]}
                suffixIcon={null}
                defaultValue={[dayjs().subtract(7, 'day'), dayjs()]}
                allowClear={true}
              />
            </S.DatePickerWrapper>
          </S.FilterItem>

          <S.FilterItem>
            <Select placeholder="Select operation" style={{ width: '100%' }}>
              <Option value="all">All operations</Option>
              <Option value="update">Update</Option>
              <Option value="delete">Delete</Option>
              <Option value="create">Create</Option>
            </Select>
          </S.FilterItem>

          <S.FilterItem>
            <Select placeholder="Select action" style={{ width: '100%' }}>
              <Option value="login">Login</Option>
              <Option value="edit">Edit settings</Option>
              <Option value="remove">Remove member</Option>
            </Select>
          </S.FilterItem>
        </S.FilterBar>

        <Table
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          style={{ marginTop: 24 }}
        />
      </S.WrapperBody>
    </SettingBody>
  );
};

export default WorkspaceTeamTransparencyLog;
