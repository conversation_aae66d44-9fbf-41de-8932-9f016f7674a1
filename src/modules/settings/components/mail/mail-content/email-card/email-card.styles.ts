import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import styled from 'styled-components';

export const BoxSection = styled.div`
  background-color: ${themeColors?.newtralLightest};
  border: 1px solid ${themeColors?.newtral};
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 12px;
`;

export const BoxTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${themeColors?.newtral};
  padding-bottom: 16px;
  margin-bottom: 16px;
`;

export const BoxSubTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
`;

export const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  margin-bottom:24px;

  label {
    font-weight: ${fontWeight.semiBold};
  }

  .ant-select-selector {
    padding:12px;
  }
    button{
    width:fit-content;
    text
    }
`;

export const FormInput = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
`;

export const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: 16px;
  button {
    width: fit-content;
  }
`;
