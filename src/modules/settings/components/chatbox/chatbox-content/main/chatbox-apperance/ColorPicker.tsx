import React, { useState } from 'react';
import * as S from './ColorPicker.styles';

interface ColorPickerProps {
  defaultColor?: string;
  label?: string;
  onChange?: (color: string) => void;
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  defaultColor = '#006124',
  label,
  onChange
}) => {
  const [color, setColor] = useState(defaultColor);
  const [showPicker, setShowPicker] = useState(false);

  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    onChange?.(newColor);
  };

  const handleHexInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    
    // Add # if not present
    if (!value.startsWith('#')) {
      value = '#' + value;
    }
    
    // Validate hex color
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (hexRegex.test(value) || value === '#') {
      setColor(value);
      if (hexRegex.test(value)) {
        onChange?.(value);
      }
    }
  };

  return (
    <S.ColorPickerContainer>
      {label && <S.ColorLabel>{label}</S.ColorLabel>}
      <S.ColorPickerWrapper>
        <S.HexDisplay onClick={() => setShowPicker(!showPicker)}>
          {color}
        </S.HexDisplay>
        <S.Divider />
        <S.ColorSection>
          <S.ColorDisplay color={color} />
          <S.DropdownIcon />
          <S.ColorInput
            type="color"
            value={color}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleColorChange(e.target.value)}
          />
        </S.ColorSection>
      </S.ColorPickerWrapper>
    </S.ColorPickerContainer>
  );
};

export default ColorPicker; 