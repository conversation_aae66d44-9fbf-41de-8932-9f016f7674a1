import { Col, Row } from 'antd';

import * as S from './Inbox.styles';
import InboxMenus from '../../components/inbox/inbox-menus/InboxMenus';
import InboxContent from '../../components/inbox/inbox-content/InboxContent';
import { useParams } from 'react-router-dom';

function Inbox() {
  const { section } = useParams();
  return (
    <S.InboxContainer>
      <Row gutter={[4, 4]}>
        <Col xs={24} xl={5}>
          <InboxMenus />
        </Col>
        <Col xs={24} xl={19}>
          <InboxContent section={section} />
        </Col>
      </Row>
    </S.InboxContainer>
  );
}

export default Inbox;
