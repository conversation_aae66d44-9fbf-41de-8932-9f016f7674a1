import themeColors from '@/shared/styles/themes/default/colors';
import { Form, Tag } from 'antd';
import styled from 'styled-components';
import { css } from 'styled-components';

export const FilterSection = styled.div`
  padding: 12px;
  margin-right:20px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.neutral_50};
  }
   .ant-form-item{
        margin-bottom:12px !important;
    }
`;

export const FilterWrap = styled.div`
  padding: 10px;
  border-radius: 10px;
  background-color: ${(props) => props.theme.colors.newtralLightest};
  border: 1px solid #e8e8e8 !important;
  .ant-select .ant-select-selector {
    border-radius: 10px !important;
  }
  .ant-select:hover .ant-select-selector,
  .ant-select:focus .ant-select-selector,
  .ant-select-focused .ant-select-selector {
    border: 1px solid ${(props) => props.theme.colors.primary} !important;
    border-radius: 10px !important;
  }

  .ant-select .ant-select-selector .ant-select-selection-item {
    color: ${(props) => props.theme.colors.neutral} !important;
  }
`;
export const Title = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  button {
    padding: 0;
    height: auto;
    min-width: auto;
  }
`;

export const FormItem = styled(Form.Item)<{
  $margin?: string;
  $isRememberMe?: boolean;
}>`
  ${({ $margin }) =>
    $margin &&
    css`
      margin: ${$margin} !important;
    `}

  ${({ $isRememberMe }) =>
    $isRememberMe &&
    css`
      margin: 0 !important;

      .ant-form-item-control-input-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .ant-checkbox-label {
          display: none;
        }
      }
    `}
`;

export const MainFilterDropdownWrapper = styled.div`
  position: relative;
  z-index: 2;
`;

export const ButtonModalDropdown = styled.button`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background: ${(props) => props.theme.colors.newtralLightest};
  cursor: pointer;
  font-size: ${(props) => props.theme.fontSize.base};
  color: ${(props) => props.theme.colors.newtralLight};
  height: 39px;
  padding: 8px 16px;
  border-radius: ${(props) => props.theme.radius.normalRadius};
  border: 1px solid ${(props) => props.theme.colors.newtral};
`;

export const ModalDropdownBorder = styled.div`
  top: 100%;
  left: 0;
  right: 0;
`;

export const BoxDropDown = styled.div`
  padding: 8px 10px;
  background: ${(props) => props.theme.colors.newtralLightest};
  border: 1px solid ${(props) => props.theme.colors.newtral};
  border-radius: ${(props) => props.theme.radius.normalRadius};
  margin-top: 4px;
  overflow-y: auto;
  box-shadow: ${(props) => props.theme.shadow.normalShadow};
`;

export const SearchWrapper = styled.div`
  padding: 8px;
  border: 1px solid ${(props) => props.theme.colors.newtral};
  display: flex;
  align-items: center;
  border-radius: ${(props) => props.theme.radius.normalRadius};
  gap: 8px;
`;

export const SearchIconDropdown = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  button {
    margin-top: 12px;
    height: 40px;
    width: fit-content;
  }
`;

export const SearchInputDropdown = styled.input`
  border: none;
  outline: none;
  width: 100%;
  font-size: ${(props) => props.theme.fontSize.base};
  color: ${(props) => props.theme.colors.newtralLight};

  &::placeholder {
    color: ${(props) => props.theme.colors.newtralDarker};
  }
`;

export const ListOptions = styled.div`
  max-height: 272px;
`;

export const DropdownBorder = styled.div`
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: ${(props) => props.theme.fontSize.base};
  color:${themeColors?.neutral};
  &:hover {
    background: ${(props) => props.theme.colors.secondaryLight};
  }
`;

export const Label = styled(Tag)`
  background-color: ${themeColors?.primary};
  color: ${themeColors?.newtralLightest};
  font-weight: 600;
  border-radius: 999px;
  font-size: 14px;
  padding: 4px 8px;
  margin: 12px 0;
  letter-spacing: 0%;
`;
