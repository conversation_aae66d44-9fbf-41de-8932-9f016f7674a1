import Select from '@/shared/components/common/Select';
import { Option } from 'antd/es/mentions';
import styled, { css } from 'styled-components';
interface WrapProps {
  isSignUpMobile?: boolean;
}

export const MultipleLangWrap = styled.div<WrapProps>`
  position: absolute;
  top: 36px;
  right: 36px;
  display: flex;
  align-items: center;

  @media (max-width: 480px) {
    ${(props) =>
      props.isSignUpMobile
        ? css`
            position: static;
            flex-direction: column;
            align-items: flex-end;
          `
        : css`
            bottom: 16px;
            right: 16px;
            flex-direction: column;
            align-items: flex-end;
          `}
  }
`;
export const ChangeLang = styled(Select)`
  min-width: 125px;
  width: fit-content;
  height: 40px !important;

  .ant-select-selector {
    border: 1px solid ${(props) => props?.theme?.colors?.neutral} !important;
    padding: 0 !important;
    min-height: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center;

    .ant-select-selection-item {
      display: flex !important;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;
      padding: 0 12px;
      width: 100%;
    }
  }

  .ant-select-arrow {
    margin-top: -4px;
  }
`;

export const LangOption = styled(Option)`
  display: flex;
  align-items: center;
  gap: 4px;
  height: 40px;
`;
