import { ReactSVG } from 'react-svg';
import SettingBody from '../../../setting/body/SettingBody';

import * as S from './email-delivery.styles';
import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import Typography from '@/shared/components/common/Typography';
import { useTranslation } from 'react-i18next';
import Button from '@/shared/components/common/Button';
import AccessSwitch from '@/shared/components/common/AccessSwitch/AccessSwitch';
import AutoSave from '../../../shared/autosave/AutoSave';

const EmailDelivery = () => {
  const { t } = useTranslation('emailSetting');

  const key = 'customSmtp'
  const settings = {
    customSmtp: false 
  };

  const handleToggle = (key: string) => {
    // Handle logic toggle
  };

  return (
    <SettingBody
      title={t('emailDelivery.title')}
      headerRight={<AutoSave/>
      }
    >
      <S.PlansWarning>
        <S.WraperSection>
          <Typography>
            {t('emailDelivery.description')}
          </Typography>
        </S.WraperSection>
      </S.PlansWarning>

      <S.BoxSection>
        <S.BoxDesc>
          <Typography variant="h5">{t('emailDelivery.ipStatusTitle')}</Typography>
          <Button type="primary">
            {t('emailDelivery.installPlugin')}
          </Button>
        </S.BoxDesc>
      </S.BoxSection>

      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('emailDelivery.customSmtpTitle')}
          </Typography>
        </S.BoxTitle>
        <S.BoxSubTitle>
          <Typography color={themeColors.neutral}>
            {t('emailDelivery.customSmtpDescription')}
          </Typography>
          <AccessSwitch
            checked={settings[key]}
            onChange={() => handleToggle(key)}
            value={key}
          />
        </S.BoxSubTitle>
      </S.BoxSection>
    </SettingBody>
  );
};

export default EmailDelivery;
