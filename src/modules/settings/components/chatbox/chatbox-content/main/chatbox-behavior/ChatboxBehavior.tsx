import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/shared/components/common/Button';
import SettingBody from '@/modules/settings/components/setting/body/SettingBody';

import * as S from './ChatboxBehavior.styles';

import icCheck from '@/assets/icons/setting/ic-check-circle.svg';
import checkIcon from '@/assets/icons/inbox/ic-check-radio.svg';
import closeIcon from '@/assets/icons/inbox/ic-close-radio.svg';
import AutoSave from '@/modules/settings/components/shared/autosave/AutoSave';

const ChatboxBehavior = () => {
  const { t } = useTranslation('chatbox');
  const [isPublic, setIsPublic] = useState(true);

  return (
    <SettingBody
      title={t('chatbox.chatbox-behavior')}
      headerRight={<AutoSave/>}
    >
      <S.ChatboxBehaviorContent>
        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.visitor')}</h2>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Ask visitors for their email address</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Ask visitors for their phone number</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Force visitors to identify themselves (email or phone)</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.files')}</h2>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Allow files to be sent from the chatbox</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Allow audio recordings to be sent from the chatbox</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.knowledge-base')}</h2>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>
              Show a link to knowledge base in the chatbox (if ZestChat
              Knowledge Base is configured)
            </p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>
              Knowledge base only mode on the chatbox (if link to knowledge base
              is enabled)
            </p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.status')}</h2>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>
              Show an alert when status reports dead (if ZestChat Status is
              configured)
            </p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.hide-chatbox')}</h2>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Hide the chatbox if all operators are away</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Hide the chatbox on mobile devices</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Advanced chatbox restrictions</p>

            <Button width="146px" type="default">
              Go to restrictions
            </Button>
          </S.ChatboxBehaviorContentItemFooter>
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.privacy')}</h2>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Operator privacy mode (eg. disable read markers in chatbox)</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>
              Operators can see what visitors write in real-time (MagicType)
            </p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Show the availability tooltip when chatbox is closed</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.other')}</h2>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Suggest to play a wait game when operators are slow to reply</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
        </S.ChatboxBehaviorContentItem>
      </S.ChatboxBehaviorContent>
    </SettingBody>
  );
};

export default ChatboxBehavior;
