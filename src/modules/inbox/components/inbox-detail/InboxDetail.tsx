import React, { useRef, useState, useEffect, Suspense } from 'react';
import { Image, Tooltip, Skeleton } from 'antd';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import { LoadingOutlined, CloseCircleTwoTone } from '@ant-design/icons';

import AvatarWithStatus from '../../../../shared/components/common/Avatar';
import MessageInput from '../message-input/MessageInput';

import {
  sendAgentMessage,
  closeConversation,
  openConversation,
  listenUserTyping,
  offUserTyping,
} from '../../../../core/services/socket/socket';

import { Message } from '../../interfaces/inbox';
import { useMessageList } from '../../hooks/useMessageList';
import { useUser } from '@/core/context/UserContext';
import { useScrollHandler } from '../../hooks/useScrollHandler';
import {
  InboxMessageStatus,
  InboxMessageType,
  InboxSender,
} from '@/modules/settings/helpers/enums/inbox.enums';
import { getShortcutsList } from '@/modules/settings/api/chatbox';
import type { Shortcut } from '@/modules/settings/models/chatbox.model';

import * as S from './InboxDetail.styles';

import avatarAdmin from '@/assets/images/avatar-default.png';
import check from '@/assets/icons/common/ic-check.svg';
import flag from '@/assets/icons/common/ic-flag.svg';
import defaultAvatar from '@/assets/images/avatar-default.png';
import icArrowDown from '@/assets/icons/inbox/ic-new-message.svg';
import icBarY from '@/assets/icons/inbox/ic-bar-y.svg';
import iconTagGroup from '@/assets/icons/inbox/ic-tag-group.svg';
import iconReply from '@/assets/icons/inbox/ic-reply.svg';
import iconEdit from '@/assets/icons/common/ic-edit.svg';
import iconCopy from '@/assets/icons/common/ic-copy.svg';
import iconDelete from '@/assets/icons/common/ic-delete.svg';
import pleaseSelectConversation from '@/assets/images/inbox/please-select-conversation.png';
interface InboxDetailProps {
  isSidebarOpen: boolean;
}

const InboxDetailContent: React.FC<InboxDetailProps> = ({ isSidebarOpen }) => {
  const { t } = useTranslation('inbox');
  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get('conversationId');
  const {
    messages,
    loading,
    loadingMore,
    hasNextPage,
    loadMore,
    addMessage,
    updateMessage,
  } = useMessageList({ conversationId });
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [selectedReminder, setSelectedReminder] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState('');
  const [guestTyping, setGuestTyping] = useState(false);
  const isSelfSendingRef = useRef(false);
  const [pendingImageScroll, setPendingImageScroll] = useState(false);
  const [pendingImageLoads, setPendingImageLoads] = useState(0);
  const [lastMessageId, setLastMessageId] = useState<string | null>(null);
  const [shortcuts, setShortcuts] = useState<Shortcut[]>([]);
  const [shortcutsPage, setShortcutsPage] = useState(1);
  const [shortcutsHasMore, setShortcutsHasMore] = useState(true);
  const [shortcutsLoading, setShortcutsLoading] = useState(false);
  const [shortcutsKeyword, setShortcutsKeyword] = useState('');
  const shortcutsListRef = useRef<HTMLDivElement>(null);
  const messageRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
    message: Message | null;
    messageId?: string;
  }>({
    x: 0,
    y: 0,
    visible: false,
    message: null,
    messageId: undefined,
  });

  const user = useUser();
  const currentUserId = user?.id;

  const messageEndRef = useRef<HTMLDivElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  // Use custom scroll hook
  const {
    wasAtBottom,
    showNewMessageNotice,
    setShowNewMessageNotice,
    handleScroll,
    handleLoadMore,
    scrollToBottom,
  } = useScrollHandler({
    loadingMore,
    hasNextPage,
    loadMore,
    pendingImageScroll,
    messageContainerRef,
    messageEndRef,
  });

  // Check if user is at bottom (gắn event listener)
  useEffect(() => {
    const container = messageContainerRef.current;
    if (!container) return;
    container.addEventListener('scroll', handleScroll);
    container.addEventListener('scroll', handleLoadMore);
    return () => {
      container.removeEventListener('scroll', handleScroll);
      container.removeEventListener('scroll', handleLoadMore);
    };
  }, [handleScroll, handleLoadMore]);

  // Real-time: scroll or show notice based on wasAtBottom and new message
  useEffect(() => {
    if (messages.length === 0) return;
    const newestId = messages[0].id;
    if (lastMessageId && newestId !== lastMessageId) {
      if (wasAtBottom) {
        setTimeout(() => {
          scrollToBottom();
        }, 0);
        setShowNewMessageNotice(false);
      } else {
        setShowNewMessageNotice(true);
      }
    }
    setLastMessageId(newestId);
  }, [
    messages,
    wasAtBottom,
    scrollToBottom,
    setShowNewMessageNotice,
    lastMessageId,
  ]);

  const prevConversationId = useRef<string | null>(null);
  const isFirstMount = useRef(true);

  useEffect(() => {
    if (isFirstMount.current) {
      if (conversationId) {
        openConversation(conversationId);
        prevConversationId.current = conversationId;
      }
      isFirstMount.current = false;
      return;
    }

    if (conversationId) {
      if (
        prevConversationId.current &&
        prevConversationId.current !== conversationId
      ) {
        closeConversation(prevConversationId.current);
      }
      if (prevConversationId.current !== conversationId) {
        openConversation(conversationId);
        prevConversationId.current = conversationId;
      }
    } else {
      if (prevConversationId.current) {
        closeConversation(prevConversationId.current);
        prevConversationId.current = null;
      }
    }

    return () => {
      if (prevConversationId.current) {
        closeConversation(prevConversationId.current);
        prevConversationId.current = null;
      }
    };
  }, [conversationId]);

  useEffect(() => {
    const handleUserTyping = (data: any) => {
      setGuestTyping(!!data.isTyping);
      if (data.isTyping) {
        if ((window as any).guestTypingTimeout)
          clearTimeout((window as any).guestTypingTimeout);
        (window as any).guestTypingTimeout = setTimeout(
          () => setGuestTyping(false),
          2000,
        );
      }
    };
    listenUserTyping(handleUserTyping);
    return () => {
      offUserTyping(handleUserTyping);
      setGuestTyping(false);
      if ((window as any).guestTypingTimeout)
        clearTimeout((window as any).guestTypingTimeout);
    };
  }, []);

  const handleTabClick = (tab: string) => {
    setActiveTab(activeTab === tab ? null : tab);
    if (tab === 'Edit') setInputValue('Hello');
    if (tab === 'Note') setInputValue(' ');
  };

  const handleSendMessage = (
    content: string,
    type: InboxMessageType = InboxMessageType.Text,
    metadata: any = {},
  ) => {
    if ((!content.trim() && type === InboxMessageType.Text) || !conversationId)
      return;

    const now = new Date();
    const temp_id = uuidv4();
    const newMessage: Message = {
      id: temp_id,
      content,
      sender: InboxSender.Agent,
      user:
        currentUserId && user?.firstName && user?.lastName && user?.avatar
          ? {
              id: currentUserId,
              firstName: user.firstName,
              lastName: user.lastName,
              avatar: user.avatar,
            }
          : null,
      type:
        type === InboxMessageType.Image
          ? InboxMessageType.Image
          : type === InboxMessageType.Note
            ? InboxMessageType.Note
            : InboxMessageType.Text,
      status: InboxMessageStatus.Sending,
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
      metadata,
    };

    console.log(newMessage);
    isSelfSendingRef.current = true;
    addMessage(newMessage);

    // Always scroll to the bottom when sending photos or notes (local preview)
    if (type === InboxMessageType.Image || type === InboxMessageType.Note) {
      setTimeout(() => {
        scrollToBottom();
      }, 0);
    }

    // If sending an image, increase the number of images waiting to be loaded
    if (type === InboxMessageType.Image) {
      setPendingImageLoads((prev) => prev + 1);
    }

    sendAgentMessage(
      {
        conversationId,
        message: {
          content,
          type,
          metadata,
          temp_id,
        },
      },
      (res: any) => {
        if (res.success && res.messageId) {
          updateMessage(temp_id, {
            id: res.messageId,
            status: InboxMessageStatus.Sent,
          });
        } else {
          updateMessage(temp_id, { status: InboxMessageStatus.Failed });
        }
      },
    );

    setInputValue('');
    setActiveTab(null);
  };

  // Fetch shortcuts
  const fetchShortcuts = async (page = 1, keyword = '') => {
    setShortcutsLoading(true);
    try {
      const res = await getShortcutsList({ page, limit: 10, keyword });
      if (page === 1) {
        setShortcuts(res.data || []);
      } else {
        setShortcuts((prev) => [...prev, ...(res.data || [])]);
      }
      setShortcutsHasMore(res.hasNextPage);
    } finally {
      setShortcutsLoading(false);
    }
  };

  // When clicking the Shortcuts tab or changing keywords
  useEffect(() => {
    if (activeTab === 'Shortcuts') {
      setShortcutsPage(1);
      fetchShortcuts(1, shortcutsKeyword);
    }
    // eslint-disable-next-line
  }, [activeTab, shortcutsKeyword]);

  // Scroll load more
  useEffect(() => {
    if (activeTab !== 'Shortcuts') return;
    const handleScroll = () => {
      const el = shortcutsListRef.current;
      if (!el || shortcutsLoading || !shortcutsHasMore) return;
      if (el.scrollTop + el.clientHeight >= el.scrollHeight - 40) {
        setShortcutsPage((prev) => {
          const next = prev + 1;
          fetchShortcuts(next, shortcutsKeyword);
          return next;
        });
      }
    };
    const el = shortcutsListRef.current;
    if (el) el.addEventListener('scroll', handleScroll);
    return () => {
      if (el) el.removeEventListener('scroll', handleScroll);
    };
    // eslint-disable-next-line
  }, [activeTab, shortcutsLoading, shortcutsHasMore, shortcutsKeyword]);

  // When entering in input, if you are in the Shortcuts tab then search
  useEffect(() => {
    if (activeTab === 'Shortcuts') {
      setShortcutsKeyword(inputValue);
    }
    // eslint-disable-next-line
  }, [inputValue, activeTab]);

  // When typing ! automatically switches to Shortcuts tab and search
  useEffect(() => {
    if (inputValue.startsWith('!')) {
      setActiveTab('Shortcuts');
      setShortcutsKeyword(inputValue.slice(1));
    }
    // eslint-disable-next-line
  }, [inputValue]);

  const MENU_WIDTH = 180;

  const handleContextMenu = (e: React.MouseEvent, message: Message) => {
    e.preventDefault();
    e.stopPropagation();
    const ref = messageRefs.current[message.id];
    if (ref) {
      const rect = ref.getBoundingClientRect();
      let x;
      if (rect.width < MENU_WIDTH) {
        x = rect.left;
      } else {
        x = rect.right - MENU_WIDTH;
      }
      if (x + MENU_WIDTH > window.innerWidth) {
        x = window.innerWidth - MENU_WIDTH - 8;
      }
      if (x < 8) x = 8;
      setContextMenu({
        x: x,
        y: rect.bottom,
        visible: true,
        message,
        messageId: message.id,
      });
    }
  };

  const closeContextMenu = () => {
    setContextMenu((prev) => ({ ...prev, visible: false }));
  };

  useEffect(() => {
    if (contextMenu.visible) {
      const close = () => closeContextMenu();
      window.addEventListener('click', close);
      window.addEventListener('scroll', close, true);
      return () => {
        window.removeEventListener('click', close);
        window.removeEventListener('scroll', close, true);
      };
    }
  }, [contextMenu.visible]);

  const handleCopyText = () => {
    if (contextMenu.message?.content) {
      navigator.clipboard.writeText(contextMenu.message.content);
    }
  };

  const handleReply = () => console.log('Reply to:', contextMenu.message?.id);
  const handleEdit = () =>
    console.log('Edit message:', contextMenu.message?.id);
  const handleDeleteMessage = () =>
    console.log('Delete message:', contextMenu.message?.id);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Shortcuts':
        return (
          <S.TabPanel>
            <S.ShortcutsList ref={shortcutsListRef}>
              {shortcuts.map((item) => (
                <S.ShortcutsItem
                  key={item.id}
                  onClick={() => setInputValue(item.message)}
                >
                  <span>{item.shortcut}</span>
                  <p>{item.message}</p>
                </S.ShortcutsItem>
              ))}
              {shortcutsLoading && (
                <S.NoShortcutsFound>Loading...</S.NoShortcutsFound>
              )}
              {!shortcutsLoading && shortcuts.length === 0 && (
                <S.NoShortcutsFound>No shortcuts found</S.NoShortcutsFound>
              )}
            </S.ShortcutsList>
          </S.TabPanel>
        );
      case 'Note':
        return null;
      case 'Reminder':
        return (
          <S.TabPanel>
            <S.ShortcutItem>
              <p>{t('inboxDetail.reminder1')}</p>
            </S.ShortcutItem>
            <S.ShortcutItem>
              <p>{t('inboxDetail.reminder2')}</p>
            </S.ShortcutItem>
            <S.ShortcutItem
              onClick={() => {
                const reminderText = '12:00 20/04/2025';
                setInputValue((prev) => prev + reminderText);
                setSelectedReminder('12:00 20/04/2025');
                setTimeout(() => {
                  if (inputRef.current) {
                    inputRef.current.focus();
                    inputRef.current.selectionStart =
                      inputRef.current.selectionEnd = (
                        inputValue + reminderText
                      ).length;
                  }
                }, 0);
              }}
            >
              <p>{t('inboxDetail.reminderTomorrow')}</p>
            </S.ShortcutItem>
          </S.TabPanel>
        );
      case 'Knowledge Base':
        return (
          <S.TabPanel>
            <S.ShortcutItem>
              <S.KnowBaseItem>Category 1</S.KnowBaseItem>
              <p>{t('inboxDetail.articleTitle1')}</p>
            </S.ShortcutItem>
            <S.ShortcutItem>
              <S.KnowBaseItem>Category 2</S.KnowBaseItem>
              <p>{t('inboxDetail.articleTitle2')}</p>
            </S.ShortcutItem>
          </S.TabPanel>
        );
      default:
        return null;
    }
  };

  const formatTime = (isoString: string) => {
    if (!isoString) return '';
    const date = new Date(isoString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  const renderSkeleton = () => (
    <>
      {[...Array(12)].map((_, i) =>
        i % 2 === 1 ? (
          <S.MessageRowUser key={i}>
            <S.MessageBubbleRight>
              <Skeleton.Input active size="small" style={{ width: 180 }} />
            </S.MessageBubbleRight>
            <S.MessageAvatarWrapper style={{ marginLeft: 8 }}>
              <Skeleton.Avatar active size={40} />
            </S.MessageAvatarWrapper>
          </S.MessageRowUser>
        ) : (
          <S.MessageRow key={i}>
            <S.MessageAvatarWrapper>
              <Skeleton.Avatar active size={40} />
            </S.MessageAvatarWrapper>
            <S.MessageBubbleLeft>
              <Skeleton.Input active size="small" style={{ width: 150 }} />
            </S.MessageBubbleLeft>
          </S.MessageRow>
        ),
      )}
    </>
  );

  const renderContextMenu = () => {
    if (!contextMenu.visible || !contextMenu.message) return null;
    return (
      <S.ContextMenu
        style={{
          top: contextMenu.y,
          left: contextMenu.x,
          position: 'fixed',
          width: MENU_WIDTH,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <S.ContextMenuItem
          style={{ borderBottom: '1px solid #eee' }}
          onClick={handleReply}
        >
          <img src={iconReply} alt="Reply" />
          Reply
        </S.ContextMenuItem>
        <S.ContextMenuItem onClick={handleEdit}>
          <img src={iconEdit} alt="Edit" />
          Edit
        </S.ContextMenuItem>
        <S.ContextMenuItem onClick={handleCopyText}>
          <img src={iconCopy} alt="Copy" />
          Copy text
        </S.ContextMenuItem>
        <S.ContextMenuSeparator />
        <S.ContextMenuItem onClick={handleDeleteMessage} danger>
          <img src={iconDelete} alt="Delete" />
          Delete
        </S.ContextMenuItem>
      </S.ContextMenu>
    );
  };

  const [barMenu, setBarMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
  }>({ visible: false, x: 0, y: 0 });
  const barIconRef = useRef<HTMLImageElement>(null);

  const handleBarMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const rect = barIconRef.current?.getBoundingClientRect();
    if (rect) {
      setBarMenu({
        visible: true,
        x: rect.left - 100,
        y: rect.top - 150,
      });
    }
  };

  useEffect(() => {
    if (barMenu.visible) {
      const close = () => setBarMenu((prev) => ({ ...prev, visible: false }));
      window.addEventListener('click', close);
      return () => window.removeEventListener('click', close);
    }
  }, [barMenu.visible]);

  const renderBarMenu = () => {
    if (!barMenu.visible) return null;
    return (
      <S.SmallPopupMenu style={{ left: barMenu.x, top: barMenu.y }}>
        <S.SmallPopupMenuItem>Share location</S.SmallPopupMenuItem>
        <S.SmallPopupMenuItem>Contact</S.SmallPopupMenuItem>
        <S.SmallPopupMenuItem>Audio</S.SmallPopupMenuItem>
      </S.SmallPopupMenu>
    );
  };

  if (!conversationId) {
    return (
      <S.ContainerConversation>
        <img src={pleaseSelectConversation} alt="" />
        <p>{t('inboxDetail.selectConversation')}</p>
      </S.ContainerConversation>
    );
  }

  return (
    <S.Container>
      {renderContextMenu()}
      <S.Header>
        <S.HeaderLeft>
          <AvatarWithStatus
            avatarSrc={defaultAvatar}
            flagSrc={flag}
            isOnline={true}
          />
          <S.Info>
            <S.Name>Guest</S.Name>
          </S.Info>
        </S.HeaderLeft>
        <S.HeaderRight>
          <S.MarkResolvedButton>
            {t('inboxDetail.markResolved')}
            <Image src={check} preview={false} />{' '}
          </S.MarkResolvedButton>
        </S.HeaderRight>
      </S.Header>

      <S.MainContent>
        <S.MessageContainer
          isSidebarOpen={isSidebarOpen}
          ref={messageContainerRef}
        >
          {loading ? (
            renderSkeleton()
          ) : messages.length === 0 ? (
            <S.EmptyState>{t('inboxDetail.noMessage')}</S.EmptyState>
          ) : (
            messages
              .slice()
              .reverse()
              .map((msg, idx) => {
                const isAgent = msg.sender === InboxSender.Agent;

                const setMsgRef = (el: HTMLDivElement | null) => {
                  messageRefs.current[msg.id] = el;
                };

                return (
                  <React.Fragment key={msg.id || idx}>
                    {isAgent ? (
                      <S.MessageRowUser>
                        {msg.type === InboxMessageType.Image &&
                        msg.metadata?.fileUrl ? (
                          <>
                            <S.MessageTime>
                              {formatTime(msg.createdAt)}
                              {msg.status === InboxMessageStatus.Sending && (
                                <LoadingOutlined
                                  style={{ marginLeft: 6, fontSize: 12 }}
                                  spin
                                />
                              )}
                              {msg.status === InboxMessageStatus.Failed && (
                                <Tooltip title="Send failed">
                                  <CloseCircleTwoTone
                                    twoToneColor="#ff4d4f"
                                    style={{ marginLeft: 6, fontSize: 12 }}
                                  />
                                </Tooltip>
                              )}
                            </S.MessageTime>
                            <S.MessageImage>
                              <Image
                                src={msg.metadata.fileUrl}
                                alt="image"
                                preview={true}
                                onLoad={() => {
                                  setPendingImageLoads((prev) => {
                                    const next = Math.max(prev - 1, 0);
                                    if (next === 0) {
                                      scrollToBottom();
                                    }
                                    return next;
                                  });
                                  if (pendingImageScroll) {
                                    setPendingImageScroll(false);
                                  }
                                }}
                              />
                            </S.MessageImage>
                          </>
                        ) : msg.type === InboxMessageType.Note ? (
                          <S.NoteContainer>
                            <S.NoteMeta>
                              <img src={iconTagGroup} alt="" />
                              Left this private note
                            </S.NoteMeta>
                            <S.NoteRow>
                              <S.NoteBubbleRight>
                                {msg.content}
                                <div
                                  style={{
                                    fontSize: 12,
                                    color: '#fff',
                                    textAlign: 'left',
                                    marginTop: 2,
                                  }}
                                >
                                  {formatTime(msg.createdAt)}
                                  {msg.status ===
                                    InboxMessageStatus.Sending && (
                                    <LoadingOutlined
                                      style={{ marginLeft: 6, fontSize: 12 }}
                                      spin
                                    />
                                  )}
                                  {msg.status === InboxMessageStatus.Failed && (
                                    <Tooltip title="Send failed">
                                      <CloseCircleTwoTone
                                        twoToneColor="#ff4d4f"
                                        style={{ marginLeft: 6, fontSize: 12 }}
                                      />
                                    </Tooltip>
                                  )}
                                </div>
                              </S.NoteBubbleRight>
                            </S.NoteRow>
                          </S.NoteContainer>
                        ) : (
                          <S.MessageBubbleRight
                            ref={setMsgRef}
                            onContextMenu={(e) => handleContextMenu(e, msg)}
                          >
                            {msg.content}
                            <div
                              style={{
                                fontSize: 12,
                                color: '#fff',
                                textAlign: 'left',
                                marginTop: 2,
                              }}
                            >
                              {formatTime(msg.createdAt)}
                              {msg.status === InboxMessageStatus.Sending && (
                                <LoadingOutlined
                                  style={{ marginLeft: 6, fontSize: 12 }}
                                  spin
                                />
                              )}
                              {msg.status === InboxMessageStatus.Failed && (
                                <Tooltip title="Send failed">
                                  <CloseCircleTwoTone
                                    twoToneColor="#ff4d4f"
                                    style={{ marginLeft: 6, fontSize: 12 }}
                                  />
                                </Tooltip>
                              )}
                            </div>
                          </S.MessageBubbleRight>
                        )}
                      </S.MessageRowUser>
                    ) : (
                      <S.MessageRow>
                        <S.MessageAvatarWrapper>
                          <S.MessageAvatar
                            src={avatarAdmin}
                            alt={msg.user?.firstName}
                          />
                          <S.MessageColumnView>
                            <S.MessageSenderName>
                              {msg.user?.firstName || 'Guest'}
                            </S.MessageSenderName>
                            {msg.type === InboxMessageType.Image &&
                            msg.metadata?.fileUrl ? (
                              <S.MessageImageLeft>
                                <Image
                                  src={msg.metadata.fileUrl}
                                  alt="image"
                                  onLoad={() => {
                                    setPendingImageLoads((prev) => {
                                      const next = Math.max(prev - 1, 0);
                                      if (next === 0) {
                                        scrollToBottom();
                                      }
                                      return next;
                                    });
                                    if (pendingImageScroll) {
                                      setPendingImageScroll(false);
                                    }
                                  }}
                                  preview={true}
                                />
                              </S.MessageImageLeft>
                            ) : (
                              <S.MessageBubbleLeft
                                ref={setMsgRef}
                                onContextMenu={(e) => handleContextMenu(e, msg)}
                              >
                                {msg.content}
                                <div
                                  style={{
                                    fontSize: 12,
                                    opacity: 0.7,
                                    textAlign: 'right',
                                    marginTop: 2,
                                  }}
                                >
                                  {formatTime(msg.createdAt)}
                                  {msg.status ===
                                    InboxMessageStatus.Sending && (
                                    <LoadingOutlined
                                      style={{ marginLeft: 6, fontSize: 12 }}
                                      spin
                                    />
                                  )}
                                  {msg.status === InboxMessageStatus.Failed && (
                                    <Tooltip title="Send failed">
                                      <CloseCircleTwoTone
                                        twoToneColor="#ff4d4f"
                                        style={{ marginLeft: 6, fontSize: 12 }}
                                      />
                                    </Tooltip>
                                  )}
                                </div>
                              </S.MessageBubbleLeft>
                            )}
                          </S.MessageColumnView>
                        </S.MessageAvatarWrapper>
                      </S.MessageRow>
                    )}
                  </React.Fragment>
                );
              })
          )}
          <div ref={messageEndRef} />
          {showNewMessageNotice && (
            <S.NewMessageNoticeButton onClick={scrollToBottom}>
              <img src={icArrowDown} alt="arrow down" className="arrow-icon" />
            </S.NewMessageNoticeButton>
          )}
        </S.MessageContainer>
      </S.MainContent>

      {renderTabContent()}

      {/* Guest is typing indicator */}
      {guestTyping && (
        <S.TypingLoading>
          <img src={defaultAvatar} alt="" />
          <S.TypingIndicator>
            <span className="dot" />
            <span className="dot" />
            <span className="dot" />
          </S.TypingIndicator>
        </S.TypingLoading>
      )}

      <S.FooterPadding>
        <S.Footer
          $hasActiveTab={!!activeTab}
          $activeTab={activeTab || undefined}
        >
          <S.ActionIcons>
            <S.FooterActionIcons>
              <S.IconProps isActive={false}>
                {t('inboxDetail.reply')}
              </S.IconProps>
              <S.IconProps
                isActive={activeTab === 'Edit'}
                onClick={() => handleTabClick('Edit')}
              >
                {t('messageInput.edit')}
              </S.IconProps>
              <S.IconProps
                isActive={activeTab === 'Note'}
                onClick={() => handleTabClick('Note')}
              >
                {t('messageInput.note')}
              </S.IconProps>
              <S.IconProps
                isActive={activeTab === 'Reminder'}
                onClick={() => handleTabClick('Reminder')}
              >
                {t('messageInput.reminder')}
              </S.IconProps>
              <S.IconProps
                isActive={activeTab === 'Shortcuts'}
                onClick={() => handleTabClick('Shortcuts')}
              >
                {t('inboxDetail.shortcuts')}
              </S.IconProps>
              <S.IconProps
                isActive={activeTab === 'Knowledge Base'}
                onClick={() => handleTabClick('Knowledge Base')}
              >
                {t('inboxDetail.knowledgeBase')}
              </S.IconProps>
            </S.FooterActionIcons>

            <img
              ref={barIconRef}
              src={icBarY}
              alt=""
              onClick={handleBarMenuClick}
              style={{ cursor: 'pointer' }}
            />
            {renderBarMenu()}
          </S.ActionIcons>

          <MessageInput
            activeTab={activeTab}
            selectedReminder={selectedReminder}
            inputValue={inputValue}
            setInputValue={setInputValue}
            setActiveTab={setActiveTab}
            setSelectedReminder={setSelectedReminder}
            onSendMessage={handleSendMessage}
          />
        </S.Footer>
      </S.FooterPadding>
    </S.Container>
  );
};

// Main component wrapped with Suspense for useLazyLoadQuery
const InboxDetail: React.FC<InboxDetailProps> = ({ isSidebarOpen }) => {
  return (
    <Suspense
      fallback={
        <S.Container>
          <S.Header>
            <S.HeaderLeft>
              <Skeleton.Avatar active size={40} />
              <S.Info>
                <Skeleton.Input active size="small" style={{ width: 120 }} />
              </S.Info>
            </S.HeaderLeft>
            <S.HeaderRight>
              <Skeleton.Button active size="small" />
            </S.HeaderRight>
          </S.Header>
          <S.MainContent>
            <S.MessageContainer isSidebarOpen={isSidebarOpen}>
              {[...Array(8)].map((_, i) => (
                <S.MessageRow key={i}>
                  <Skeleton.Avatar active size={40} />
                  <Skeleton.Input
                    active
                    size="small"
                    style={{ width: 200, marginLeft: 8 }}
                  />
                </S.MessageRow>
              ))}
            </S.MessageContainer>
          </S.MainContent>
        </S.Container>
      }
    >
      <InboxDetailContent isSidebarOpen={isSidebarOpen} />
    </Suspense>
  );
};

export default InboxDetail;
