import React, { useMemo } from 'react';
;
import { MailMenusEnums } from '@/modules/settings/helpers/enums/mail';
import EmailCard from './email-card/EmailCard';
import EmailDomain from './email-domain/EmailDomain';
import EmailDelivery from './email-delivery/EmailDelivery';

interface MailContentProps {
  section?: string;
}
const mailComponentMap: Record<MailMenusEnums, React.ReactNode> = {
  [MailMenusEnums.EMAIL_BEHAVIOR]: <EmailCard />,
  [MailMenusEnums.EMAIL_DOMAIN]: <EmailDomain />,
  [MailMenusEnums.EMAIL_DELIVERY]: <EmailDelivery />,
};

const MailContent: React.FC<MailContentProps> = ({ section }) => {
  const typeMail = Object.values(MailMenusEnums).includes(section as MailMenusEnums)
    ? (section as MailMenusEnums)
    : MailMenusEnums.EMAIL_BEHAVIOR; 

  const renderMailContent = useMemo(() => mailComponentMap[typeMail], [typeMail]);

  return <>{renderMailContent}</>;
};

export default MailContent;
