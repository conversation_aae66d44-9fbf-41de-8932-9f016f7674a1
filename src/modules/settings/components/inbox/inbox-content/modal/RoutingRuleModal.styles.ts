import fontWeight from "@/shared/styles/themes/default/fontWeight";
import styled from "styled-components";

export const ModalBody = styled.div`
`;

export const ModalStepContent = styled.div`
  padding:0 10px
`;

export const ModalStepsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 170px;
  position: relative;
  margin-bottom:24px;
  height:100%;
`;

export const Diver = styled.div`
position: absolute;
    top: 20%;
    left:31%;
    width: 40%;
    height: 1px;
    background-color: ${({ theme }) => theme.colors.newtral};
    z-index: 0;
`

export const StepItem = styled.div<{ active?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  padding: 0 10px;
  cursor: pointer;

  font-weight: ${({ active }) => (active ? fontWeight.semiBold : fontWeight.medium)};
  color: ${({ active, theme }) => (active ? '#253A8E' : theme.colors.newtralLight)};
  z-index: 2;
  .ant-image{
    z-index: 2;
    position: relative;
    img{
      position: absolute;
          z-index: 4;
    }
  }

  p{
    text-transform: uppercase;
    font-size: 10px;
}
  }
`;
