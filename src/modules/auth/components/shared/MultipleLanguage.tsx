import React from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import { langOptions } from '../../helpers/data/signIn';
import { OptionsInterface } from '@/core/model/common';
import Typography from '@/shared/components/common/Typography';
import themeColors from '@/shared/styles/themes/default/colors';

import * as S from './MultipleLanguage.styles';

const MultipleLanguage = () => {
  const { t } = useTranslation('auth');
  const { pathname } = useLocation();

  const isMobile = window.innerWidth <= 480;
  const isSignUpMobile = pathname === '/auth/sign-up' && isMobile;

  return (
    <S.MultipleLangWrap isSignUpMobile={isSignUpMobile}>
      <S.ChangeLang defaultValue={langOptions?.[0]?.value} popupClassName="auth-lang">
        {langOptions?.map((lang: OptionsInterface) => (
          <S.LangOption key={lang?.key}>
            <Typography color={themeColors?.neutral}>
              {t(`language.${lang?.label}`)}
            </Typography>
          </S.LangOption>
        ))}
      </S.ChangeLang>
    </S.MultipleLangWrap>
  );
};

export default MultipleLanguage;
