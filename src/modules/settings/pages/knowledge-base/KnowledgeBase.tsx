import { Col, Row } from 'antd';
import { useParams } from 'react-router-dom';

import * as S from './KnowledgeBase.styles';
import KnowledgeBaseMenu from '../../components/knowledge-base/knowledge-base-menu/KnowledgeBaseMenu';
import KnowledgeBaseContent from '../../components/knowledge-base/knowledge-base-content/main/KnowledgeBaseContent';

function KnowledgeBase() {
    const { section } = useParams();

    return (
        <S.KnowledgeBaseContainer>
            <Row gutter={[4, 4]}>
                <Col xs={24} xl={5}>
                    <KnowledgeBaseMenu />
                </Col>
                <Col xs={24} xl={19}>
                   <KnowledgeBaseContent section={section}/>
                </Col>
            </Row>
        </S.KnowledgeBaseContainer>
    );
}

export default KnowledgeBase;
