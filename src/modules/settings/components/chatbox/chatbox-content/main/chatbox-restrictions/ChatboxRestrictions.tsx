import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/shared/components/common/Button';
import Modal from '@/shared/components/common/Modal';
import SettingBody from '@/modules/settings/components/setting/body/SettingBody';

import * as S from './ChatboxRestrictions.styles';

import icCheck from '@/assets/icons/setting/ic-check-circle.svg';
import checkIcon from '@/assets/icons/inbox/ic-check-radio.svg';
import closeIcon from '@/assets/icons/inbox/ic-close-radio.svg';
import icLinkModal from '@/assets/icons/setting/ic-link-modal.svg';
import icLocation from '@/assets/icons/setting/ic-location-modal.svg';
import icGlobalModal from '@/assets/icons/setting/ic-global-modal.svg';
import icTrashModal from '@/assets/icons/setting/ic-trash-modal.svg';
import imgFlagUnitedStates from '@/assets/images/settings/img-flag-united-states.png';

const ChatboxRestrictions = () => {
  const { t } = useTranslation('chatbox');
  const [isPublic, setIsPublic] = useState(true);
  const [isOpenAllowedPage, setIsOpenAllowedPage] = useState(false);
  const [isOpenBlockedPage, setIsOpenBlockedPage] = useState(false);
  const [isOpenBlockedCountry, setIsOpenBlockedCountry] = useState(false);
  const [isOpenBlockedLocale, setIsOpenBlockedLocale] = useState(false);
  const [isOpenBlockedUser, setIsOpenBlockedUser] = useState(false);
  const [allowedPages, setAllowedPages] = useState<string[]>([]);
  const [blockedPages, setBlockedPages] = useState<string[]>([]);
  const [blockedCountries, setBlockedCountries] = useState<string[]>([]);
  const [blockedLocales, setBlockedLocales] = useState<string[]>([]);
  const [blockedIPs, setBlockedIPs] = useState<string[]>([]);
  const [inputAllowedPage, setInputAllowedPage] = useState('');
  const [inputBlockedPage, setInputBlockedPage] = useState('');
  const [inputBlockedCountry, setInputBlockedCountry] = useState('');
  const [inputBlockedLocale, setInputBlockedLocale] = useState('');
  const [inputBlockedIP, setInputBlockedIP] = useState('');

  return (
    <>
      <S.ChatboxSecurityGlobalStyle />
      <SettingBody
        title={t('chatbox.chatbox-restrictions')}
        headerRight={
          <S.ChatboxBehaviorHeaderRight>
            <img src={icCheck} alt="check" />
            <p>{t('chatbox.automatically-saved')}</p>
          </S.ChatboxBehaviorHeaderRight>
        }
      >
        <S.ChatboxBehaviorContent>
          <S.ChatboxBehaviorContentItem>
            <S.ChatboxBehaviorContentItemFooter>
              <p>Place support in vacation (hide chatbox)</p>

              <S.AccessRadioRow>
                <S.AccessSwitchWrapper>
                  <S.AccessSwitchInput
                    checked={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                  />
                  <S.AccessSwitchSlider checked={isPublic}>
                    <span className="switch-icon">
                      <img src={isPublic ? checkIcon : closeIcon} alt="" />
                    </span>
                  </S.AccessSwitchSlider>
                </S.AccessSwitchWrapper>
              </S.AccessRadioRow>
            </S.ChatboxBehaviorContentItemFooter>

            <S.ChatboxBehaviorContentItemFooter>
              <p>Show chatbox only on pages</p>

              <Button
                width="169px"
                type="default"
                onClick={() => {
                  setIsOpenAllowedPage(true);
                }}
              >
                Add an allowed page
              </Button>
            </S.ChatboxBehaviorContentItemFooter>

            {allowedPages.length > 0 && (
              <S.ChatboxBehaviorContentItemFooter style={{ flexDirection: 'column', alignItems: 'flex-start', gap: 0 }}>
                <S.RestrictionListContainer>
                  <S.RestrictionListHeader>
                    <S.RestrictionListIcon src={icLinkModal} alt="url" /> URL
                  </S.RestrictionListHeader>
                  {allowedPages.map((url, idx) => (
                    <S.RestrictionListRow key={url + idx}>
                      <S.RestrictionListText>{url}</S.RestrictionListText>
                      <S.RestrictionTrashButton onClick={() => setAllowedPages(allowedPages.filter((_, i) => i !== idx))}>
                        <img src={icTrashModal} alt="delete" style={{ width: 18, height: 18 }} />
                      </S.RestrictionTrashButton>
                    </S.RestrictionListRow>
                  ))}
                </S.RestrictionListContainer>
              </S.ChatboxBehaviorContentItemFooter>
            )}

            <Modal
              width={700}
              isOpen={isOpenAllowedPage}
              onClose={() => setIsOpenAllowedPage(false)}
              title={t('chatbox.allow-a-page-by-url')}
              footer={
                <>
                  <Button
                    width="76px"
                    type="default"
                    onClick={() => setIsOpenAllowedPage(false)}
                  >
                    {t('chatbox.cancel')}
                  </Button>

                  <Button
                    width="243px"
                    type="primary"
                    onClick={() => {
                      if (inputAllowedPage.trim()) {
                        setAllowedPages([...allowedPages, inputAllowedPage.trim()]);
                        setInputAllowedPage('');
                        setIsOpenAllowedPage(false);
                      }
                    }}
                  >
                    Add allowed page
                  </Button>
                </>
              }
            >
              <div>
                <S.ChatboxBehaviorContentLabel>
                  URL <span style={{ color: 'red' }}>*</span>
                </S.ChatboxBehaviorContentLabel>
                <S.ChatboxBehaviorContentInput>
                  <img src={icLinkModal} alt="" style={{ marginRight: 8 }} />
                  <input
                    type="text"
                    value={inputAllowedPage}
                    onChange={(e) => setInputAllowedPage(e.target.value)}
                    placeholder='Enter URL (use "*" for any sub-page, "" for anything)'
                  />
                </S.ChatboxBehaviorContentInput>
              </div>
            </Modal>

            <S.ChatboxBehaviorContentItemFooter>
              <p>Hide chatbox on pages</p>

              <Button
                width="169px"
                type="default"
                onClick={() => {
                  setIsOpenBlockedPage(true);
                }}
              >
                Add a blocked page
              </Button>
            </S.ChatboxBehaviorContentItemFooter>

            {blockedPages.length > 0 && (
              <S.ChatboxBehaviorContentItemFooter style={{ flexDirection: 'column', alignItems: 'flex-start', gap: 0 }}>
                <S.RestrictionListContainer>
                  <S.RestrictionListHeader>
                    <S.RestrictionListIcon src={icLinkModal} alt="url" /> URL
                  </S.RestrictionListHeader>
                  {blockedPages.map((url, idx) => (
                    <S.RestrictionListRow key={url + idx}>
                      <S.RestrictionListText>{url}</S.RestrictionListText>
                      <S.RestrictionTrashButton onClick={() => setBlockedPages(blockedPages.filter((_, i) => i !== idx))}>
                        <img src={icTrashModal} alt="delete" style={{ width: 18, height: 18 }} />
                      </S.RestrictionTrashButton>
                    </S.RestrictionListRow>
                  ))}
                </S.RestrictionListContainer>
              </S.ChatboxBehaviorContentItemFooter>
            )}

            <Modal
              width={700}
              isOpen={isOpenBlockedPage}
              onClose={() => setIsOpenBlockedPage(false)}
              title={t('chatbox.block-a-page-by-url')}
              footer={
                <>
                  <Button
                    width="76px"
                    type="default"
                    onClick={() => setIsOpenBlockedPage(false)}
                  >
                    {t('chatbox.cancel')}
                  </Button>

                  <Button
                    width="243px"
                    type="primary"
                    onClick={() => {
                      if (inputBlockedPage.trim()) {
                        setBlockedPages([...blockedPages, inputBlockedPage.trim()]);
                        setInputBlockedPage('');
                        setIsOpenBlockedPage(false);
                      }
                    }}
                  >
                    Add blocked page
                  </Button>
                </>
              }
            >
              <div>
                <S.ChatboxBehaviorContentLabel>
                  URL <span style={{ color: 'red' }}>*</span>
                </S.ChatboxBehaviorContentLabel>
                <S.ChatboxBehaviorContentInput>
                  <img src={icLinkModal} alt="" style={{ marginRight: 8 }} />
                  <input
                    type="text"
                    value={inputBlockedPage}
                    onChange={(e) => setInputBlockedPage(e.target.value)}
                    placeholder='Enter URL (use "*" for any sub-page, "" for anything)'
                  />
                </S.ChatboxBehaviorContentInput>
              </div>
            </Modal>

            <S.ChatboxBehaviorContentItemFooter>
              <p>Hide chatbox for countries</p>

              <Button
                width="169px"
                type="default"
                onClick={() => {
                  setIsOpenBlockedCountry(true);
                }}
              >
                Add a blocked country
              </Button>
            </S.ChatboxBehaviorContentItemFooter>

            {blockedCountries.length > 0 && (
              <S.ChatboxBehaviorContentItemFooter style={{ flexDirection: 'column', alignItems: 'flex-start', gap: 0 }}>
                <S.RestrictionListContainer>
                  <S.RestrictionListHeader>
                    <S.RestrictionListIcon src={icGlobalModal} alt="country" /> Country
                  </S.RestrictionListHeader>
                  {blockedCountries.map((country, idx) => (
                    <S.RestrictionListRow key={country + idx}>
                      <S.RestrictionFlagIcon src={imgFlagUnitedStates} alt="flag" />
                      <S.RestrictionListText>{country}</S.RestrictionListText>
                      <S.RestrictionTrashButton onClick={() => setBlockedCountries(blockedCountries.filter((_, i) => i !== idx))}>
                        <img src={icTrashModal} alt="delete" style={{ width: 18, height: 18 }} />
                      </S.RestrictionTrashButton>
                    </S.RestrictionListRow>
                  ))}
                </S.RestrictionListContainer>
              </S.ChatboxBehaviorContentItemFooter>
            )}

            <Modal
              width={700}
              isOpen={isOpenBlockedCountry}
              onClose={() => setIsOpenBlockedCountry(false)}
              title={t('chatbox.hide-chatbox-for-countries')}
              footer={
                <>
                  <Button
                    width="76px"
                    type="default"
                    onClick={() => setIsOpenBlockedCountry(false)}
                  >
                    {t('chatbox.cancel')}
                  </Button>

                  <Button
                    width="243px"
                    type="primary"
                    onClick={() => {
                      if (inputBlockedCountry.trim()) {
                        setBlockedCountries([...blockedCountries, inputBlockedCountry.trim()]);
                        setInputBlockedCountry('');
                        setIsOpenBlockedCountry(false);
                      }
                    }}
                  >
                    Add blocked country
                  </Button>
                </>
              }
            >
              <div>
                <S.ChatboxBehaviorContentLabel>
                  Country <span style={{ color: 'red' }}>*</span>
                </S.ChatboxBehaviorContentLabel>
                <S.ChatboxBehaviorContentInput>
                  <img src={icGlobalModal} alt="" style={{ marginRight: 8 }} />
                  <input
                    type="text"
                    value={inputBlockedCountry}
                    onChange={(e) => setInputBlockedCountry(e.target.value)}
                    placeholder="Enter country"
                  />
                </S.ChatboxBehaviorContentInput>
              </div>
            </Modal>

            <S.ChatboxBehaviorContentItemFooter>
              <p>Hide chatbox for locales</p>

              <Button
                width="169px"
                type="default"
                onClick={() => {
                  setIsOpenBlockedLocale(true);
                }}
              >
                Add a blocked locale
              </Button>
            </S.ChatboxBehaviorContentItemFooter>

            {blockedLocales.length > 0 && (
              <S.ChatboxBehaviorContentItemFooter style={{ flexDirection: 'column', alignItems: 'flex-start', gap: 0 }}>
                <S.RestrictionListContainer>
                  <S.RestrictionListHeader>
                    <S.RestrictionListIcon src={icLocation} alt="locale" /> Locale
                  </S.RestrictionListHeader>
                  {blockedLocales.map((locale, idx) => (
                    <S.RestrictionListRow key={locale + idx}>
                      <S.RestrictionFlagIcon src={imgFlagUnitedStates} alt="flag" />
                      <S.RestrictionListText>{locale}</S.RestrictionListText>
                      <S.RestrictionTrashButton onClick={() => setBlockedLocales(blockedLocales.filter((_, i) => i !== idx))}>
                        <img src={icTrashModal} alt="delete" style={{ width: 18, height: 18 }} />
                      </S.RestrictionTrashButton>
                    </S.RestrictionListRow>
                  ))}
                </S.RestrictionListContainer>
              </S.ChatboxBehaviorContentItemFooter>
            )}

            <Modal
              width={700}
              isOpen={isOpenBlockedLocale}
              onClose={() => setIsOpenBlockedLocale(false)}
              title={t('chatbox.add-a-blocked-locale')}
              footer={
                <>
                  <Button
                    width="76px"
                    type="default"
                    onClick={() => setIsOpenBlockedLocale(false)}
                  >
                    {t('chatbox.cancel')}
                  </Button>

                  <Button
                    width="243px"
                    type="primary"
                    onClick={() => {
                      if (inputBlockedLocale.trim()) {
                        setBlockedLocales([...blockedLocales, inputBlockedLocale.trim()]);
                        setInputBlockedLocale('');
                        setIsOpenBlockedLocale(false);
                      }
                    }}
                  >
                    Add blocked locale
                  </Button>
                </>
              }
            >
              <div>
                <S.ChatboxBehaviorContentLabel>
                  Locale <span style={{ color: 'red' }}>*</span>
                </S.ChatboxBehaviorContentLabel>
                <S.ChatboxBehaviorContentInput>
                  <img src={icLocation} alt="" style={{ marginRight: 8 }} />
                  <input
                    type="text"
                    value={inputBlockedLocale}
                    onChange={(e) => setInputBlockedLocale(e.target.value)}
                    placeholder="Enter locale"
                  />
                </S.ChatboxBehaviorContentInput>
              </div>
            </Modal>

            <S.ChatboxBehaviorContentItemFooter>
              <p>Hide chatbox for IPs</p>

              <Button
                width="169px"
                type="default"
                onClick={() => {
                  setIsOpenBlockedUser(true);
                }}
              >
                Add a blocked IP
              </Button>
            </S.ChatboxBehaviorContentItemFooter>

            {blockedIPs.length > 0 && (
              <S.ChatboxBehaviorContentItemFooter style={{ flexDirection: 'column', alignItems: 'flex-start', gap: 0 }}>
                <S.RestrictionListContainer>
                  <S.RestrictionListHeader>
                    <S.RestrictionListIcon src={icLocation} alt="ip" /> IP
                  </S.RestrictionListHeader>
                  {blockedIPs.map((ip, idx) => (
                    <S.RestrictionListRow key={ip + idx}>
                      <S.RestrictionListText>{ip}</S.RestrictionListText>
                      <S.RestrictionTrashButton onClick={() => setBlockedIPs(blockedIPs.filter((_, i) => i !== idx))}>
                        <img src={icTrashModal} alt="delete" style={{ width: 18, height: 18 }} />
                      </S.RestrictionTrashButton>
                    </S.RestrictionListRow>
                  ))}
                </S.RestrictionListContainer>
              </S.ChatboxBehaviorContentItemFooter>
            )}

            <Modal
              width={700}
              isOpen={isOpenBlockedUser}
              onClose={() => setIsOpenBlockedUser(false)}
              title={t('chatbox.add-a-blocked-ip')}
              footer={
                <>
                  <Button
                    width="76px"
                    type="default"
                    onClick={() => setIsOpenBlockedUser(false)}
                  >
                    {t('chatbox.cancel')}
                  </Button>

                  <Button
                    width="243px"
                    type="primary"
                    onClick={() => {
                      if (inputBlockedIP.trim()) {
                        setBlockedIPs([...blockedIPs, inputBlockedIP.trim()]);
                        setInputBlockedIP('');
                        setIsOpenBlockedUser(false);
                      }
                    }}
                  >
                    Add blocked IP
                  </Button>
                </>
              }
            >
              <div>
                <S.ChatboxBehaviorContentLabel>
                  IP <span style={{ color: 'red' }}>*</span>
                </S.ChatboxBehaviorContentLabel>
                <S.ChatboxBehaviorContentInput>
                  <img src={icLocation} alt="" style={{ marginRight: 8 }} />
                  <input
                    type="text"
                    value={inputBlockedIP}
                    onChange={(e) => setInputBlockedIP(e.target.value)}
                    placeholder="Enter IP"
                  />
                </S.ChatboxBehaviorContentInput>
              </div>
            </Modal>

            <S.ChatboxBehaviorContentItemFooter>
              <p>Blocked visitors</p>

              <Button
                width="241px"
                type="danger"
                className="privacy-ignored-btn"
              >
                Clear all block rules (IP & Email)
              </Button>
            </S.ChatboxBehaviorContentItemFooter>
          </S.ChatboxBehaviorContentItem>
        </S.ChatboxBehaviorContent>
      </SettingBody>
    </>
  );
};

export default ChatboxRestrictions;
