import React, { useMemo } from 'react';
import { KNOWLEDGE_BASE_MENUS_ENUMS } from '@/modules/settings/helpers/enums/knowledgebase';

import SetupKnowledgeBase from '../setup-knowledge-base/SetupKnowledgeBase';
import AdvancedSetting from '../advance-settings/AdvancedSetting';
import CustomizeKnowledgeBase from '../customize-knowledge-base/CustomizeKnowledgeBase';

interface KnowledgeBaseProps {
  section?: string;
}

const knowledgeBaseComponentMap: Record<KNOWLEDGE_BASE_MENUS_ENUMS, React.ReactNode> = {
  [KNOWLEDGE_BASE_MENUS_ENUMS.SETUP_KNOWLEDGE_BASE]: <SetupKnowledgeBase />,
  [KNOWLEDGE_BASE_MENUS_ENUMS.CUSTOMIZE_KNOWLEDGE_BASE]: <CustomizeKnowledgeBase />,
  [KNOWLEDGE_BASE_MENUS_ENUMS.ADVANCED_SETTINGS]: <AdvancedSetting />,
};

const KnowledgeBaseContent: React.FC<KnowledgeBaseProps> = ({ section }) => {
  const matchedSection = Object.values(KN<PERSON>LEDGE_BASE_MENUS_ENUMS).includes(section as KNOWLEDGE_BASE_MENUS_ENUMS)
    ? (section as KNOWLEDGE_BASE_MENUS_ENUMS)
    : KNOWLEDGE_BASE_MENUS_ENUMS.SETUP_KNOWLEDGE_BASE;

  const renderComponent = useMemo(() => knowledgeBaseComponentMap[matchedSection], [matchedSection]);

  return <>{renderComponent}</>;
};

export default KnowledgeBaseContent;
