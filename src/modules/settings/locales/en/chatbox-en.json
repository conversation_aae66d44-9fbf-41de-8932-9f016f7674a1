{"chatbox-menu": {"chatbox-setting": "CHATBOX SETTING", "customize-your-inbox": "Customize your inbox", "chatbox-apperance": "Chatbox apperance", "chatbox-security": "Chatbox security", "chatbox-restrictions": "Chatbox restrictions", "push-notifications": "Push notifications"}, "chatbox": {"chatbox-behavior": "Chatbox behavior", "visitor": "Visitor", "files": "Files", "knowledge-base": "Knowledge base", "status": "Status", "hide-chatbox": "Hide chatbox", "privacy": "Privacy", "other": "Other", "automatically-saved": "Automatically saved", "chatbox-security": "Chatbox security", "enfore-total-privacy": "Enforce total privacy", "privacy-choices-honored": "Privacy choices honored", "total-privacy-is-inactive": "Total privacy is inactive", "if-you-organization-has-stringent": "If you organization has stringent privacy policies in place, you can choose to never auto-connect your users to the ZestChat chatbox when they access your website. This will disable the auto-creation of chat sessions and thereby the auto-placement of session cookies. Chat sessions will thus only be created if a user opens the ZestChat chatbox. Enabling Total Privacy will degrade ZestChat features such as MagicMap and Analytics, since only users engaging in conversations will now appear as online.", "yes-degrade-zestchat-features": "Yes, degrade ZestChat features", "cancel": "Cancel", "ignore-user-privacy-choice": "Ignore user privacy choice", "in-doing-this-you-agree-to-adjust": "In doing this, you agree to adjust your own terms of use to reflect your choice. This will let MagicType and MagicBrowse work for users with Do Not Track enabled. ZestChat cannot be help responsible for any user complaint, or any privacy infrigement.", "i-respect-my-users": "I respect my users", "lets-be-sneaky": "Let's be sneaky", "push-notifications": "Push notifications", "firebase-cloud-messaging": "Firebase cloud messaging", "apple-push-notifications-service": "Apple push notification service", "chatbox-restrictions": "Chatbox restrictions", "allow-a-page-by-url": "Allow a page by URL", "block-a-page-by-url": "Blocked user with locale", "hide-chatbox-for-countries": "Block a page by URL", "add-a-blocked-country": "Add a blocked country", "add-a-blocked-ip": "Block the chatbox for the following IPs", "add-a-blocked-user": "Block users from country", "chatbox-apperance": "Chatbox apperance", "customization": "Customization", "appearance": "Appearance"}}