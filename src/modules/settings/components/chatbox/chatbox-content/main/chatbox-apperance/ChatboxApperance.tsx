import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/shared/components/common/Button';
import SettingBody from '@/modules/settings/components/setting/body/SettingBody';
import ColorPicker from './ColorPicker';

import * as S from './ChatboxApperance.styles';

import icCheck from '@/assets/icons/setting/ic-check-circle.svg';
import checkIcon from '@/assets/icons/inbox/ic-check-radio.svg';
import closeIcon from '@/assets/icons/inbox/ic-close-radio.svg';
import icArrowUp from '@/assets/icons/setting/ic-arrow-up.svg';
import icArrowRight from '@/assets/icons/setting/ic-arr-right-green.svg';
import icBar from '@/assets/icons/setting/ic-bar-apperance.svg';
import icBubble from '@/assets/icons/setting/ic-buble-apperance.svg';
import icLightMode from '@/assets/icons/setting/ic-light-mode.svg';
import icDarkMode from '@/assets/icons/setting/ic-dark-mode.svg';
import icTickApperance from '@/assets/icons/setting/ic-tick-apperance.svg';
import imgFullColors from '@/assets/images/settings/img-full-colors.png';
import { ReactSVG } from 'react-svg';

const colorOptions = [
  '#222', '#006124', '#6C3DD1', '#1E88E5', '#4DD0E1', '#81C784',
  '#FFEB3B', '#FFD600', '#FF9800', '#E65100', '#D81B60', 'conic-gradient(red, yellow, green, blue, red)'
];

const ChatboxApperance = () => {
  const { t } = useTranslation('chatbox');
  const [isPublic, setIsPublic] = useState(true);

  // Widget settings states
  const [widgetSettings, setWidgetSettings] = useState({
    minimizedType: 'bubble', // 'bar' or 'bubble'
    theme: 'light', // 'light' or 'dark'
  });

  // Color states
  const [colors, setColors] = useState({
    bubble: '#006124',
    iconColor: '#FFFFFF',
    chatBackground: '#F8F9FA',
    primaryColor: '#006124',
    customerBubble: '#E3F2FD',
    customerText: '#1976D2',
    agentBubble: '#F3E5F5',
    agentText: '#7B1FA2',
    systemMessages: '#FFF3E0',
  });

  // Collapse states for each section
  const [collapsedSections, setCollapsedSections] = useState({
    customization: false,
    appearance: false,
    position: false,
    mobile: false,
    additional: false,
  });

  const [selectedColor, setSelectedColor] = useState(colorOptions[0]);

  // Update widget settings function
  const updateWidgetSetting = (
    settingKey: keyof typeof widgetSettings,
    value: string,
  ) => {
    setWidgetSettings((prev) => ({
      ...prev,
      [settingKey]: value,
    }));
  };

  // Update color function
  const updateColor = (colorKey: keyof typeof colors, newColor: string) => {
    setColors((prev) => ({
      ...prev,
      [colorKey]: newColor,
    }));
  };

  // Save colors function
  const handleSaveColors = () => {
    console.log('Saving chatbox colors:', colors);
    // Here you would typically send the colors to your API
    // Example: saveChatboxColors(colors);
    alert('Colors saved successfully!');
  };

  // Toggle collapse function
  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  return (
    <SettingBody title={t('chatbox.chatbox-apperance')}>
      <S.ChatboxBehaviorContent>
        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader
            onClick={() => toggleSection('customization')}
            style={{ cursor: 'pointer' }}
          >
            <h2>{t('chatbox.customization')}</h2>

            <img
              src={icArrowUp}
              alt="arrow"
              style={{
                transition: 'transform 0.2s ease',
                transform: collapsedSections.customization
                  ? 'rotate(180deg)'
                  : 'rotate(0deg)',
              }}
            />
          </S.ChatboxBehaviorContentItemHeader>

          {!collapsedSections.customization && (
            <S.ChatboxApperanceContentItemFooter>
              <p>
                Align widget with your brand by customizing it directly on your
                website
              </p>

              <S.ChatboxApperanceContentItemColumn>
                <p>Type website address</p>

                <S.InputWrapper>
                  <input type="text" placeholder="Enter your website address" />
                  <img src={icArrowRight} alt="arrow" />
                </S.InputWrapper>
              </S.ChatboxApperanceContentItemColumn>
            </S.ChatboxApperanceContentItemFooter>
          )}
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader
            onClick={() => toggleSection('appearance')}
            style={{ cursor: 'pointer' }}
          >
            <h2>{t('chatbox.appearance')}</h2>

            <img
              src={icArrowUp}
              alt="arrow"
              style={{
                transition: 'transform 0.2s ease',
                transform: collapsedSections.appearance
                  ? 'rotate(180deg)'
                  : 'rotate(0deg)',
              }}
            />
          </S.ChatboxBehaviorContentItemHeader>

          {!collapsedSections.appearance && (
            <S.ChatboxApperanceContentItemFooter>
              <S.ChatboxApperanceContentItemColumn>
                <S.ChatboxApperanceContentItemRow>
                  <p>Minimized window</p>

                  <S.ChatboxApperanceRight>
                    <S.ChatboxApperanceColumn
                      $isActive={widgetSettings.minimizedType === 'bar'}
                      onClick={() =>
                        updateWidgetSetting('minimizedType', 'bar')
                      }
                    >
                      <S.BarWrapper
                        $isActive={widgetSettings.minimizedType === 'bar'}
                      >
                        <ReactSVG src={icBar} />
                      </S.BarWrapper>
                      <p>Bar</p>
                    </S.ChatboxApperanceColumn>

                    <S.ChatboxApperanceColumn
                      $isActive={widgetSettings.minimizedType === 'bubble'}
                      onClick={() =>
                        updateWidgetSetting('minimizedType', 'bubble')
                      }
                    >
                      <S.BarWrapper
                        $isActive={widgetSettings.minimizedType === 'bubble'}
                      >
                        <ReactSVG src={icBubble} />
                      </S.BarWrapper>
                      <p>Bubble</p>
                    </S.ChatboxApperanceColumn>
                  </S.ChatboxApperanceRight>
                </S.ChatboxApperanceContentItemRow>

                <S.ChatboxApperanceContentItemRow>
                  <p>Theme and colors</p>

                  <S.ChatboxApperanceRight>
                    <S.ChatboxApperanceColumn
                      $isActive={widgetSettings.theme === 'light'}
                      onClick={() => updateWidgetSetting('theme', 'light')}
                    >
                      <S.BarWrapper
                        $isActive={widgetSettings.theme === 'light'}
                      >
                        <ReactSVG src={icLightMode} />
                      </S.BarWrapper>
                      <p>Light mode</p>
                    </S.ChatboxApperanceColumn>

                    <S.ChatboxApperanceColumn
                      $isActive={widgetSettings.theme === 'dark'}
                      onClick={() => updateWidgetSetting('theme', 'dark')}
                    >
                      <S.BarWrapper $isActive={widgetSettings.theme === 'dark'}>
                        <ReactSVG src={icDarkMode} />
                      </S.BarWrapper>
                      <p>Dark mode</p>
                    </S.ChatboxApperanceColumn>
                  </S.ChatboxApperanceRight>
                </S.ChatboxApperanceContentItemRow>

                <S.ChatboxApperanceContentItemRow>
                  <S.ChatboxApperanceLeft>
                    <img src={icTickApperance} alt="arrow" />
                    <p>Theme and colors</p>
                  </S.ChatboxApperanceLeft>
                  <S.ColorCircleRow>
                    {colorOptions.map((color, idx) => (
                      <S.ColorCircle
                        key={idx}
                        $color={color}
                        $selected={selectedColor === color}
                        onClick={() => setSelectedColor(color)}
                        style={color.includes('conic-gradient') ? { background: color } : {}}
                      />
                    ))}
                  </S.ColorCircleRow>
                </S.ChatboxApperanceContentItemRow>

                <S.ChatboxApperanceContentItemRow>
                  <S.ChatboxApperanceLeft>
                    <img src={icTickApperance} alt="arrow" />
                    <p>More color setting</p>
                  </S.ChatboxApperanceLeft>

                  <span>
                    Make the chat widget more unique by choosing a custom color
                    scheme.
                  </span>
                </S.ChatboxApperanceContentItemRow>

                <S.ColorSettingBlock>
                  <S.ColorSettingSection>
                    <S.ColorSettingSectionTitle>
                      Minimized widget
                    </S.ColorSettingSectionTitle>
                    <S.ColorSettingRow>
                      <S.ColorSettingCol>
                        <ColorPicker
                          label="Bubble"
                          defaultColor={colors.bubble}
                          onChange={(color) => updateColor('bubble', color)}
                        />
                      </S.ColorSettingCol>
                      <S.ColorSettingCol>
                        <ColorPicker
                          label="Icon color"
                          defaultColor={colors.iconColor}
                          onChange={(color) => updateColor('iconColor', color)}
                        />
                      </S.ColorSettingCol>
                    </S.ColorSettingRow>
                  </S.ColorSettingSection>
                  <S.ColorSettingDivider />
                  <S.ColorSettingSection>
                    <S.ColorSettingSectionTitle>
                      Maximized widget
                    </S.ColorSettingSectionTitle>
                    <S.ColorSettingRow>
                      <S.ColorSettingCol style={{ flex: 1 }}>
                        <ColorPicker
                          label="Chat background"
                          defaultColor={colors.chatBackground}
                          onChange={(color) =>
                            updateColor('chatBackground', color)
                          }
                        />
                      </S.ColorSettingCol>
                      <S.ColorSettingCol style={{ flex: 1 }}>
                        <S.ColorSettingLabel>Opacity</S.ColorSettingLabel>
                        <S.ColorTextInput defaultValue="10%" />
                      </S.ColorSettingCol>
                    </S.ColorSettingRow>
                    <S.ColorSettingRow>
                      <S.ColorSettingCol>
                        <ColorPicker
                          label="Primary color"
                          defaultColor={colors.primaryColor}
                          onChange={(color) =>
                            updateColor('primaryColor', color)
                          }
                        />
                      </S.ColorSettingCol>
                    </S.ColorSettingRow>
                    <S.ColorSettingRow>
                      <S.ColorSettingCol>
                        <ColorPicker
                          label="Customer's bubble"
                          defaultColor={colors.customerBubble}
                          onChange={(color) =>
                            updateColor('customerBubble', color)
                          }
                        />
                      </S.ColorSettingCol>
                      <S.ColorSettingCol>
                        <S.ColorSettingLabel>
                          Customer's text
                        </S.ColorSettingLabel>
                        <S.ColorTextInput defaultValue="10%" />
                      </S.ColorSettingCol>
                    </S.ColorSettingRow>
                    <S.ColorSettingRow>
                      <S.ColorSettingCol>
                        <ColorPicker
                          label="Agent's bubble"
                          defaultColor={colors.agentBubble}
                          onChange={(color) =>
                            updateColor('agentBubble', color)
                          }
                        />
                      </S.ColorSettingCol>
                      <S.ColorSettingCol>
                        <S.ColorSettingLabel>Agent's text</S.ColorSettingLabel>
                        <S.ColorTextInput defaultValue="10%" />
                      </S.ColorSettingCol>
                    </S.ColorSettingRow>
                    <S.ColorSettingRow>
                      <S.ColorSettingCol>
                        <ColorPicker
                          label="System messages"
                          defaultColor={colors.systemMessages}
                          onChange={(color) =>
                            updateColor('systemMessages', color)
                          }
                        />
                      </S.ColorSettingCol>
                    </S.ColorSettingRow>
                  </S.ColorSettingSection>
                </S.ColorSettingBlock>
              </S.ChatboxApperanceContentItemColumn>
            </S.ChatboxApperanceContentItemFooter>
          )}
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader
            onClick={() => toggleSection('position')}
            style={{ cursor: 'pointer' }}
          >
            <h2>Position</h2>
            <img
              src={icArrowUp}
              alt="arrow"
              style={{
                transition: 'transform 0.2s ease',
                transform: collapsedSections.position
                  ? 'rotate(180deg)'
                  : 'rotate(0deg)',
              }}
            />
          </S.ChatboxBehaviorContentItemHeader>

          {!collapsedSections.position && (
            <S.WidgetPositionTitle>Widget postion</S.WidgetPositionTitle>
          )}
          {!collapsedSections.position && (
            <S.PositionRow>
              <S.PositionCol>
                <S.PositionLabel>Align to:</S.PositionLabel>
                <S.SelectWrapper>
                  <S.AlignSelect>
                    <option>Right</option>
                    <option>Left</option>
                  </S.AlignSelect>
                  <S.SelectArrowIcon />
                </S.SelectWrapper>
              </S.PositionCol>
              <S.PositionCol>
                <S.PositionLabel>Side spacing</S.PositionLabel>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 4,
                    borderRadius: '8px',
                    border: '1px solid #e8e8e8',
                    height: '36px',
                    padding: '0 8px',
                  }}
                >
                  <S.SpacingInput type="number" defaultValue={0} />
                  <S.PxText>px</S.PxText>
                </div>
              </S.PositionCol>
              <S.PositionCol>
                <S.PositionLabel>Buttom spacing</S.PositionLabel>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 4,
                    borderRadius: '8px',
                    border: '1px solid #e8e8e8',
                    height: '36px',
                    padding: '0 8px',
                  }}
                >
                  <S.SpacingInput type="number" defaultValue={0} />
                  <S.PxText>px</S.PxText>
                </div>
              </S.PositionCol>
            </S.PositionRow>
          )}
          {!collapsedSections.position && (
            <S.VisibilityTitle>
              Visibility{' '}
              <S.VisibilityDesc>
                Control when to display the chat widget icon on your website
              </S.VisibilityDesc>
            </S.VisibilityTitle>
          )}
          {!collapsedSections.position && (
            <S.VisibilityOptions>
              <S.VisibilityLabel>
                <S.VisibilityIcon src={icTickApperance} alt="checked" />
                <S.VisibilityTextBlock>
                  <S.VisibilityTextTitle>
                    The chat widget is always visible
                  </S.VisibilityTextTitle>
                  <S.VisibilityTextDesc>
                    Display the widget icon on your site to let customers start
                    a chat
                  </S.VisibilityTextDesc>
                </S.VisibilityTextBlock>
              </S.VisibilityLabel>
              <S.VisibilityLabel>
                <S.VisibilityRadio />
                <S.VisibilityTextBlock>
                  <S.VisibilityTextTitle>
                    Hide widget until it gét activated
                  </S.VisibilityTextTitle>
                  <S.VisibilityTextDesc>
                    Once a chat is started, the widget will appear and will
                    remain visible
                  </S.VisibilityTextDesc>
                </S.VisibilityTextBlock>
              </S.VisibilityLabel>
              <S.VisibilityLabel>
                <S.VisibilityRadio />
                <S.VisibilityTextBlock>
                  <S.VisibilityTextTitle>
                    Always hide minimized widget icon
                  </S.VisibilityTextTitle>
                  <S.VisibilityTextDesc>
                    Customers won't be able to start chats unless you add a
                    clickable chat button to your website.
                  </S.VisibilityTextDesc>
                </S.VisibilityTextBlock>
              </S.VisibilityLabel>
            </S.VisibilityOptions>
          )}
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader
            onClick={() => toggleSection('mobile')}
            style={{ cursor: 'pointer' }}
          >
            <h2>Mobile chat widget</h2>
            <img
              src={icArrowUp}
              alt="arrow"
              style={{
                transition: 'transform 0.2s ease',
                transform: collapsedSections.mobile
                  ? 'rotate(180deg)'
                  : 'rotate(0deg)',
              }}
            />
          </S.ChatboxBehaviorContentItemHeader>

          {!collapsedSections.mobile && (
            <S.VisibilityTitle>
              Show chat widget on mobile
              <S.AccessRadioRow>
                <S.AccessSwitchWrapper>
                  <S.AccessSwitchInput
                    checked={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                  />
                  <S.AccessSwitchSlider checked={isPublic}>
                    <span className="switch-icon">
                      <img src={isPublic ? checkIcon : closeIcon} alt="" />
                    </span>
                  </S.AccessSwitchSlider>
                </S.AccessSwitchWrapper>
              </S.AccessRadioRow>
            </S.VisibilityTitle>
          )}
          {!collapsedSections.mobile && (
            <S.VisibilityRow>
              <S.VisibilityRadio />
              <p>Same as on desktop</p>
            </S.VisibilityRow>
          )}
          {!collapsedSections.mobile && (
            <S.VisibilityRow>
              <S.VisibilityIcon src={icTickApperance} alt="checked" />
              <p>Custom for mobile devices</p>
            </S.VisibilityRow>
          )}
          {!collapsedSections.mobile && (
            <S.MobileWidgetSection>
              <S.AppearanceSection>
                <S.AppearanceTitle>Appearance</S.AppearanceTitle>
                <S.MobileWidgetType>Mobile widget type</S.MobileWidgetType>
                <S.SelectWrapper>
                  <S.AlignSelect>
                    <option>Bubble</option>
                    <option>Bar</option>
                  </S.AlignSelect>
                  <S.SelectArrowIcon />
                </S.SelectWrapper>
              </S.AppearanceSection>

              <S.SpacingRow>
                <S.SpacingCol>
                  <S.PositionLabel>Align to:</S.PositionLabel>
                  <S.SelectWrapper>
                    <S.AlignSelect>
                      <option>Right</option>
                      <option>Left</option>
                    </S.AlignSelect>
                    <S.SelectArrowIcon />
                  </S.SelectWrapper>
                </S.SpacingCol>
                <S.PositionCol>
                  <S.PositionLabel>Side spacing</S.PositionLabel>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 4,
                      borderRadius: '8px',
                      border: '1px solid #e8e8e8',
                      height: '36px',
                      padding: '0 8px',
                    }}
                  >
                    <S.SpacingInput type="number" defaultValue={0} />
                    <S.PxText>px</S.PxText>
                  </div>
                </S.PositionCol>
                <S.PositionCol>
                  <S.PositionLabel>Buttom spacing</S.PositionLabel>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 4,
                      borderRadius: '8px',
                      border: '1px solid #e8e8e8',
                      height: '36px',
                      padding: '0 8px',
                    }}
                  >
                    <S.SpacingInput type="number" defaultValue={0} />
                    <S.PxText>px</S.PxText>
                  </div>
                </S.PositionCol>
              </S.SpacingRow>

              <S.VisibilityTitle>Visibility</S.VisibilityTitle>
              <S.VisibilityMobileWidget>
                <S.VisibilityLabel>
                  <S.VisibilityIcon src={icTickApperance} alt="checked" />
                  <S.VisibilityTextBlock>
                    <S.VisibilityTextTitle>
                      The chat widget is always visible
                    </S.VisibilityTextTitle>
                    <S.VisibilityTextDesc>
                      Display the widget icon on your site to let customers
                      start a chat
                    </S.VisibilityTextDesc>
                  </S.VisibilityTextBlock>
                </S.VisibilityLabel>
                <S.VisibilityLabel>
                  <S.VisibilityRadio />
                  <S.VisibilityTextBlock>
                    <S.VisibilityTextTitle>
                      Hide widget until it gét activated
                    </S.VisibilityTextTitle>
                    <S.VisibilityTextDesc>
                      Once a chat is started, the widget will appear and will
                      remain visible
                    </S.VisibilityTextDesc>
                  </S.VisibilityTextBlock>
                </S.VisibilityLabel>
                <S.VisibilityLabel>
                  <S.VisibilityRadio />
                  <S.VisibilityTextBlock>
                    <S.VisibilityTextTitle>
                      Always hide minimized widget icon
                    </S.VisibilityTextTitle>
                    <S.VisibilityTextDesc>
                      Customers won't be able to start chats unless you add a
                      clickable chat button to your website.
                    </S.VisibilityTextDesc>
                  </S.VisibilityTextBlock>
                </S.VisibilityLabel>
              </S.VisibilityMobileWidget>
            </S.MobileWidgetSection>
          )}
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader
            onClick={() => toggleSection('additional')}
            style={{ cursor: 'pointer' }}
          >
            <h2>Additional tweaks</h2>
            <img
              src={icArrowUp}
              alt="arrow"
              style={{
                transition: 'transform 0.2s ease',
                transform: collapsedSections.additional
                  ? 'rotate(180deg)'
                  : 'rotate(0deg)',
              }}
            />
          </S.ChatboxBehaviorContentItemHeader>

          {!collapsedSections.additional && (
            <S.VisibilityTitle>
              Show logo
              <S.VisibilityMobileWidgetRow>
                <S.ButonRelative>
                  <Button width="143px" type="default">
                    Upload your logo
                  </Button>
                </S.ButonRelative>
                <S.AccessRadioRow>
                  <S.AccessSwitchWrapper>
                    <S.AccessSwitchInput
                      checked={isPublic}
                      onChange={() => setIsPublic(!isPublic)}
                    />
                    <S.AccessSwitchSlider checked={isPublic}>
                      <span className="switch-icon">
                        <img src={isPublic ? checkIcon : closeIcon} alt="" />
                      </span>
                    </S.AccessSwitchSlider>
                  </S.AccessSwitchWrapper>
                </S.AccessRadioRow>
              </S.VisibilityMobileWidgetRow>
            </S.VisibilityTitle>
          )}
          {!collapsedSections.additional && (
            <S.VisibilityTitle>
              Show agent's photo
              <S.AccessRadioRow>
                <S.AccessSwitchWrapper>
                  <S.AccessSwitchInput
                    checked={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                  />
                  <S.AccessSwitchSlider checked={isPublic}>
                    <span className="switch-icon">
                      <img src={isPublic ? checkIcon : closeIcon} alt="" />
                    </span>
                  </S.AccessSwitchSlider>
                </S.AccessSwitchWrapper>
              </S.AccessRadioRow>
            </S.VisibilityTitle>
          )}
          {!collapsedSections.additional && (
            <S.VisibilityTitle>
              Enable sound notifications for customers
              <S.AccessRadioRow>
                <S.AccessSwitchWrapper>
                  <S.AccessSwitchInput
                    checked={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                  />
                  <S.AccessSwitchSlider checked={isPublic}>
                    <span className="switch-icon">
                      <img src={isPublic ? checkIcon : closeIcon} alt="" />
                    </span>
                  </S.AccessSwitchSlider>
                </S.AccessSwitchWrapper>
              </S.AccessRadioRow>
            </S.VisibilityTitle>
          )}
          {!collapsedSections.additional && (
            <S.VisibilityTitle>
              Let customers rate agents
              <S.AccessRadioRow>
                <S.AccessSwitchWrapper>
                  <S.AccessSwitchInput
                    checked={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                  />
                  <S.AccessSwitchSlider checked={isPublic}>
                    <span className="switch-icon">
                      <img src={isPublic ? checkIcon : closeIcon} alt="" />
                    </span>
                  </S.AccessSwitchSlider>
                </S.AccessSwitchWrapper>
              </S.AccessRadioRow>
            </S.VisibilityTitle>
          )}
          {!collapsedSections.additional && (
            <S.VisibilityTitle>
              Let customers get chat transcripts
              <S.AccessRadioRow>
                <S.AccessSwitchWrapper>
                  <S.AccessSwitchInput
                    checked={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                  />
                  <S.AccessSwitchSlider checked={isPublic}>
                    <span className="switch-icon">
                      <img src={isPublic ? checkIcon : closeIcon} alt="" />
                    </span>
                  </S.AccessSwitchSlider>
                </S.AccessSwitchWrapper>
              </S.AccessRadioRow>
            </S.VisibilityTitle>
          )}
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.VisibilityTitle>
            <Button width="60px" type="default">
              Cancel
            </Button>

            <Button width="60px" type="primary" onClick={handleSaveColors}>
              Save
            </Button>
          </S.VisibilityTitle>
        </S.ChatboxBehaviorContentItem>
      </S.ChatboxBehaviorContent>
    </SettingBody>
  );
};

export default ChatboxApperance;
