import React, { useMemo } from 'react';
import { InboxMenusEnums } from '@/modules/settings/helpers/enums/inbox';

import MessageShortcuts from './message-shortcuts/MessageShortcuts';

import * as S from './InboxContent.styles';
import CustomizeYourInbox from './customize-your-inbox/CustomizeYourInbox';
import SubInboxes from './sub-inboxes/SubInboxes';
import OperatorRouting from './operator-routing/OperatorRouting';
import AutomaticTriage from './automatic-triage/AutomaticTriage';

interface InboxContentProps {
  section?: string;
}

const InboxContent: React.FC<InboxContentProps> = ({ section }) => {
  const type = Object.values(InboxMenusEnums).includes(section as InboxMenusEnums)
    ? (section as InboxMenusEnums)
    : InboxMenusEnums.CUSTOMIZE_YOUR_INBOX;

  const renderContent = useMemo(() => {
    switch (type) {
      case InboxMenusEnums.CUSTOMIZE_YOUR_INBOX:
        return <CustomizeYourInbox/>;
      case InboxMenusEnums.SUB_INBOXES:
        return <SubInboxes/>;
      case InboxMenusEnums.OPERATOR_ROUTING:
        return <OperatorRouting/>;
      case InboxMenusEnums.AUTO_TRIAGE:
        return <AutomaticTriage/>;
      case InboxMenusEnums.MESSAGE_SHORTCUTS:
         return <MessageShortcuts />;
      default:
        return <CustomizeYourInbox/>;
    }
  }, [type]);

  return <S.ChatboxContentContainer>{renderContent}</S.ChatboxContentContainer>;
};

export default InboxContent;
