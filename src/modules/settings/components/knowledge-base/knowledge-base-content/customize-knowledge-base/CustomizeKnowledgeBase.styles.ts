import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import { Form } from 'antd';
import styled, { css } from 'styled-components';


export const BoxSection = styled.div`
  background-color: ${themeColors?.newtralLightest};
  border: 1px solid ${themeColors?.newtral};
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 12px;
`;

export const BoxTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${themeColors?.newtral};
  padding-bottom: 16px;
  margin-bottom: 16px;
`;

export const BoxSubTitle = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  padding-bottom: 16px;
`;

export const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  margin-bottom:24px;

  label {
    font-weight: ${fontWeight.semiBold};
  }

  .ant-select-selector {
    padding:12px;
  }
    button{
    width:fit-content;
    text
    }
`;

export const FormInput = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
`;

export const FormItem = styled(Form.Item)<{
  $margin?: string;
  $isRememberMe?: boolean;
}>`
  margin-bottom: 0px !important;

  ${({ $margin }) =>
    $margin &&
    css`
      margin: ${$margin} !important;
    `}
`;

export const BoxWrapField = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;

  > div {
    flex: 1;
  }
`;

export const Label = styled.span`
  position: absolute;
  top: 36px;
  right: 12px;
  font-weight: 400;
  font-size: 10px;
  z-index: 999;
  padding: 4px 12px;
  color: ${themeColors?.primary};
  background-color: ${themeColors?.primaryBackground};
  border-radius: 8px;
`;

export const LabelNumber = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 26px;
  height: 26px;
  border-radius: 8px;
  background-color: ${themeColors?.primary};
  color: ${themeColors?.newtralLighter};
`;

export const LabelText = styled.span`
  border-radius: 8px;
  padding: 8px 10px;
  background-color: #dee9e2;
  color: ${themeColors?.primary};
`;

export const WrapLogo = styled.div`
margin:24px 0 0 0`;

export const HeaderLogo = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88px;
  padding: 4px;
  border-radius: 10px;
  border-width: 1px;
  background: ${themeColors?.neutral_60};
  border: 1px solid ${themeColors?.newtral};
`;

export const FooterLogo = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88px;
  padding: 4px;
  border-radius: 10px;
  border-width: 1px;
  background: ${themeColors?.neutral_60};
  border: 1px solid ${themeColors?.newtral};
  gap:8px;
  button{
  width:fit-content;
  }
`;

export const BoxSubTitle1 = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 24px;
  button{
    width:fit-content;
  }
`;