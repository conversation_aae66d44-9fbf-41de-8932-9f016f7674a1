import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/shared/components/common/Button';
import SettingBody from '@/modules/settings/components/setting/body/SettingBody';

import * as S from './PushNotifications.styles';

import icCheck from '@/assets/icons/setting/ic-check-circle.svg';
import checkIcon from '@/assets/icons/inbox/ic-check-radio.svg';
import closeIcon from '@/assets/icons/inbox/ic-close-radio.svg';

const PushNotifications = () => {
  const { t } = useTranslation('chatbox');
  const [isPublic, setIsPublic] = useState(false);
  const [isFirebaseConfig, setIsFirebaseConfig] = useState(false);

  return (
    <SettingBody
      title={t('chatbox.push-notifications')}
      headerRight={
        <S.ChatboxBehaviorHeaderRight>
          <img src={icCheck} alt="check" />
          <p>{t('chatbox.automatically-saved')}</p>
        </S.ChatboxBehaviorHeaderRight>
      }
    >
      <S.ChatboxBehaviorContent>
        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.firebase-cloud-messaging')}</h2>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Notify user using Android</p>
            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isPublic}
                  onChange={() => setIsPublic(!isPublic)}
                />
                <S.AccessSwitchSlider checked={isPublic}>
                  <span className="switch-icon">
                    <img src={isPublic ? checkIcon : closeIcon} alt="" />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
          {isPublic && (
            <>
              <S.FirebaseConfigPanel>
                <S.FirebaseConfigRow>
                  <S.FirebaseConfigLabel>Certificate</S.FirebaseConfigLabel>
                  <Button width='265px' type="default">
                    Upload file service account (JSON)
                  </Button>
                </S.FirebaseConfigRow>
                <S.FirebaseConfigRow>
                  <S.FirebaseConfigLabel>Project ID</S.FirebaseConfigLabel>
                  <S.FirebaseConfigInput placeholder="Enter project ID" />
                </S.FirebaseConfigRow>
              </S.FirebaseConfigPanel>

              <S.FirebaseRight>
                <Button width='145px' type="primary">Verify credentials</Button>
              </S.FirebaseRight>
            </>
          )}
        </S.ChatboxBehaviorContentItem>

        <S.ChatboxBehaviorContentItem>
          <S.ChatboxBehaviorContentItemHeader>
            <h2>{t('chatbox.apple-push-notifications-service')}</h2>
          </S.ChatboxBehaviorContentItemHeader>

          <S.ChatboxBehaviorContentItemFooter>
            <p>Notify user using IOS</p>

            <S.AccessRadioRow>
              <S.AccessSwitchWrapper>
                <S.AccessSwitchInput
                  checked={isFirebaseConfig}
                  onChange={() => setIsFirebaseConfig(!isFirebaseConfig)}
                />
                <S.AccessSwitchSlider checked={isFirebaseConfig}>
                  <span className="switch-icon">
                    <img
                      src={isFirebaseConfig ? checkIcon : closeIcon}
                      alt=""
                    />
                  </span>
                </S.AccessSwitchSlider>
              </S.AccessSwitchWrapper>
            </S.AccessRadioRow>
          </S.ChatboxBehaviorContentItemFooter>
          {isFirebaseConfig && (
            <>
              <S.FirebaseConfigPanel>
                <S.FirebaseConfigRow>
                  <S.FirebaseConfigLabel>Certificate</S.FirebaseConfigLabel>
                  <Button width='265px' type="default">
                    Upload file service account (JSON)
                  </Button>
                </S.FirebaseConfigRow>
                <S.FirebaseConfigRow>
                  <S.FirebaseConfigLabel>Team ID <span>*</span></S.FirebaseConfigLabel>
                  <S.FirebaseConfigInput placeholder="Enter team ID" />
                </S.FirebaseConfigRow>
                <S.FirebaseConfigRow>
                  <S.FirebaseConfigLabel>Bundle ID <span>*</span></S.FirebaseConfigLabel>
                  <S.FirebaseConfigInput placeholder="Enter bundle ID" />
                </S.FirebaseConfigRow>
                <S.FirebaseConfigRow>
                  <S.FirebaseConfigLabel>Key ID <span>*</span></S.FirebaseConfigLabel>
                  <S.FirebaseConfigInput placeholder="Enter key ID" />
                </S.FirebaseConfigRow>
              </S.FirebaseConfigPanel>

              <S.FirebaseRight>
                <Button width='145px' type="primary">Verify credentials</Button>
              </S.FirebaseRight>
            </>
          )}
        </S.ChatboxBehaviorContentItem>
      </S.ChatboxBehaviorContent>
    </SettingBody>
  );
};

export default PushNotifications;
