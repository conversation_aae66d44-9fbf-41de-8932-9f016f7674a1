import styled, { css } from 'styled-components';
import themeColors from '@/shared/styles/themes/default/colors';
import { Form } from 'antd';

export const Container = styled.div`
  display: flex;
  gap: 16px;
`;

export const ChildSection = styled.div`
  flex:1;
display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border: 1px solid ${themeColors?.newtral};
  border-radius: 8px;
  background-color: ${themeColors.newtralLightest};
  cursor: pointer;
  transition: border-color 0.3s, box-shadow 0.3s;

  &:hover {
    box-shadow: 0px 1px 2px 1px rgba(10, 13, 20, 0.03); /* converted #0A0D1408 */
  }
`;

export const BoxContent = styled.div`
  display: flex;
  flex-direction: column;
`;

export const AdvancedBox = styled.div`
  padding: 24px;
  border: 1px solid ${themeColors.primaryLight};
  border-radius: 8px;
  background-color: ${themeColors.newtralLightest};
`;

export const BackButton = styled.button`
  margin-top: 20px;
  background: none;
  border: none;
  color: ${themeColors.primary};
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  text-decoration: underline;

  &:hover {
    opacity: 0.8;
  }
`;

export const FilterSection = styled.div`
  padding: 12px;
  margin-right:20px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.neutral_50};
  }
   .ant-form-item{
        margin-bottom:12px !important;
    }
`;

export const FilterWrap = styled.div`
  padding: 10px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.newtralLightest};
    border: 1px solid #E8E8E8 !important;

  .ant-select:hover .ant-select-selector,
  .ant-select:focus .ant-select-selector,
  .ant-select-focused .ant-select-selector {
    border: 1px solid ${(props) => props.theme.colors.primary} !important;
  }

  .ant-select .ant-select-selector .ant-select-selection-item {
    color: ${(props) => props.theme.colors.neutral} !important;
  }
`;
export const Title = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
margin-bottom:12px;
  button {
    padding: 0;
    height: auto;
    min-width: auto;
  }
`;

export const FormItem = styled(Form.Item)<{
  $margin?: string;
  $isRememberMe?: boolean;
}>`
  ${({ $margin }) =>
    $margin &&
    css`
      margin: ${$margin} !important;
    `}

  ${({ $isRememberMe }) =>
    $isRememberMe &&
    css`
      margin: 0 !important;

      .ant-form-item-control-input-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .ant-checkbox-label {
          display: none;
        }

      }
    `}
`;


export const MainFilterDropdownWrapper = styled.div`
  position: relative;
  z-index: 2;
`;