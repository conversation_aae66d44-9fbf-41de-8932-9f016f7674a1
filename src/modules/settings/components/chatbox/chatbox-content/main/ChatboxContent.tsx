import React, { useMemo } from 'react';

import { ChatBoxMenusEnums } from '@/modules/settings/helpers/enums/chatbox';
import ChatboxBehavior from './chatbox-behavior/ChatboxBehavior';
import ChatboxSecurity from './chatbox-security/ChatboxSecurity';
import PushNotifications from './push-notifications/PushNotifications';
import ChatboxRestrictions from './chatbox-restrictions/ChatboxRestrictions';
import ChatboxApperance from './chatbox-apperance/ChatboxApperance';

interface ChatboxContentProps {
  section?: string;
}

const ChatboxContent: React.FC<ChatboxContentProps> = ({ section }) => {
  const typeChatbox = Object.values(ChatBoxMenusEnums).includes(
    section as ChatBoxMenusEnums,
  )
    ? (section as ChatBoxMenusEnums)
    : ChatBoxMenusEnums.CHATBOX_BEHAVIOR;

  const renderChatboxContent = useMemo(() => {
    switch (typeChatbox) {
      case ChatBoxMenusEnums.CHATBOX_BEHAVIOR:
        return <ChatboxBehavior />;
      case ChatBoxMenusEnums.CHATBOX_APPERANCE:
        return <ChatboxApperance/>;
      case ChatBoxMenusEnums.CHATBOX_SECURITY:
        return <ChatboxSecurity />;
      case ChatBoxMenusEnums.CHATBOX_RESTRICTIONS:
        return <ChatboxRestrictions />;
      case ChatBoxMenusEnums.PUSH_NOTIFICATIONS:
        return <PushNotifications />;
      default:
        return <ChatboxBehavior />;
    }
  }, [typeChatbox]);

  const isApperancePage = typeChatbox === ChatBoxMenusEnums.CHATBOX_APPERANCE;

  return (
    <div style={{ width: '100%', height: '100%', padding: '10px 0px', margin: "0", display: "flex", gap: "10px" }}>
      {isApperancePage ? (
        <>
          <div
            style={{
              width: '60%',
            }}
          >
            {renderChatboxContent}
          </div>
          <div style={{ width: '40%'}}>
            <p>Preview</p>
          </div>
        </>
      ) : (
        <div style={{ width: '100%' }}>
          {renderChatboxContent}
        </div>
      )}
    </div>
  );
};

export default ChatboxContent;
