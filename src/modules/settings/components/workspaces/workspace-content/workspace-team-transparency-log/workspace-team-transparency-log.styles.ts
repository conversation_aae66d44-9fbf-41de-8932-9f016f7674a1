import Select from '@/shared/components/common/Select';
import themeColors from '@/shared/styles/themes/default/colors';
import styled from 'styled-components';

export const LabelActive = styled.div`
  display: flex;
  align-items: center;
  color: ${themeColors?.primary};
  gap: 8px;
  font-style: italic;
`;

export const WrapperBody = styled.div`
  background-color: ${themeColors?.newtralLightest};
  padding: 24px;
  border-radius: 10px;
  border: 1px solid #e8e8e8;
  width: 100%;
  gap: 16px;

  .ant-table-thead .ant-table-cell {
    background-color: #dee9e2;
    padding: 10px;
  }

  .ant-table-tbody .ant-table-cell {
    color: ${themeColors?.neutral};
  }
`;

export const PlansWarning = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid ${themeColors.newtral};
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid ${themeColors?.warningDarkest};
  background-color: ${themeColors?.newtralLightest};
  margin-bottom: 12px;
  button {
    height: 36px;
    width: fit-content;
  }
`;

export const WraperSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const WraperTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 12px;
  border-bottom: 1px solid ${themeColors?.newtral};
`;

export const AccessSwitchInput = styled.input.attrs({ type: 'checkbox' })`
  opacity: 0;
  width: 0;
  height: 0;
  &:checked + span {
    background: ${(props) => props.theme.colors.primary};
  }
  &:checked + span:before {
    transform: translateX(20px);
  }
  &:disabled + span {
    background: ${(props) => props.theme.colors.borderDisabled};
    cursor: not-allowed;
  }
  &:disabled + span:before {
    background: ${(props) => props.theme.colors.newtral};
  }
`;

export const AccessSwitchWrapper = styled.label`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
`;

export const AccessSwitchSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${(props) => props.theme.colors.borderDisabled};
  border-radius: 999px;
  transition: background 0.2s;
  box-shadow: ${(props) => props.theme.shadow.smallShadow};
  &:before {
    content: '';
    position: absolute;
    left: 2px;
    top: 2px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${(props) => props.theme.colors.newtralLightest};
    transition:
      transform 0.2s,
      background 0.2s;
    box-shadow: ${(props) => props.theme.shadow.smallShadow};
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 9px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    // background: ${(props) => props.theme.colors.newtralDarker};
    transform: translateY(-50%);
    transition:
      left 0.2s,
      background 0.2s;
  }
  input:checked + &::after {
    left: 22px;
    background: url('/src/assets/icons/layout/ic-checked.svg');
    height: 20px;
    width: 20px;
  }
  input:disabled + &::after {
    background: ${(props) => props.theme.colors.newtral};
  }
`;

export const FilterBar = styled.div`
  display: flex;
  gap: 12px;
  width: 100%;
  margin: 12px 0;
`;

export const FilterItem = styled.div`
  flex: 1;
  .ant-select-single {
    height: 40px;
  }
  .ant-select.ant-select-outlined.ant-select-single .ant-select-selector:hover,
  .ant-select-focused .ant-select-selector {
    border: 1px solid ${themeColors?.primary} !important;
    box-shadow:none !important;
  }
`;

export const DatePickerWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 0 12px;
  height: 40px;
  background: #fff;

  .ant-picker {
    border: none;
    box-shadow: none;
    padding: 0;
    width: 100%;
  }
  .ant-picker-range .ant-picker-active-bar {
    background-color: ${themeColors?.primary};
  }

  .ant-picker-cell-inner{
   background-color: ${themeColors?.primary} !important;
  }
`;

export const TitleBox = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${themeColors?.primary};
  div {
    line-height: 0;
  }
`;

export const Userbox = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  div {
    line-height: 0;
  }

  .ant-image .ant-image-img {
    height: 32px;
    width: 32px;
    object-fit: cover;
    object-position: center;
    border-radius: 9999px;
  }
`;

export const IpBox = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  div {
    line-height: 0;
  }
`;
