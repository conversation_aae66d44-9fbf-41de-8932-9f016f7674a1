import themeColors from '@/shared/styles/themes/default/colors';
import styled from 'styled-components';
import creditCardImage from '@/assets/images/creditcard.jpg';


export const WrapperBody = styled.div`
  background-color: ${themeColors?.newtralLightest};
  padding: 24px;
  border-radius: 10px;
  border: 1px solid #e8e8e8;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap:16px;
`;

export const CardItem = styled.div`
  display: flex;
  align-items: center;
`;

export const CardLeft = styled.div`
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  min-width: 315px;
  min-height: 180px;
  background-image: url(${creditCardImage});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

export const CardLeftHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  p {
    font-size: 20px;
  }
`;

export const CardValid = styled.div`
  display: flex;
  height:24px;
  width:59px;
  gap: 2px;
  align-items: center;
 justify-content: center;
  border-radius: 8px;
  background: #ffffff;
  border: 1px solid #e8e8e8;
  color: #333;
  font-weight: 500;
  box-shadow: var(--sds-size-depth-0) 2px 3px var(--sds-size-depth-0) #0a0d140d;
  span {
    font-size: 10px;
  }
`;

export const CardInfoLabel = styled.div`
  font-size: 10px;
  color: ${themeColors?.newtralDark};
  margin-bottom: 12px;
  display: flex;
  align-items: center;

  p {
    font-size: 14px;
    color: #333333;
  }

  span {
    font-size: 14px;
    width: 150px;
  }
`;

export const CardInfoValue = styled.div`
  font-weight: 700;
  font-size: 14px;
  color: ${themeColors?.neutral};
`;

export const CardFlex = styled.div`
  display: flex;
  gap: 10px;
  align-items: center;
`;

export const CardNumber = styled.div`
  letter-spacing: 2px;
`;

export const CardRight = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

export const CardRightRow = styled.div`
  display: flex;
  gap: 32px;
`;

export const CardRightCol = styled.div`
  min-width: 120px;
`;

export const CardWorkspaceTag = styled.div`
  display: flex;
  align-items: center;
  background: #dee9e2;
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 10px;
  font-weight: 500;
  gap: 6px;
`;
