import { Col, Row } from 'antd';

import * as S from './Mail.styles';
import { useParams } from 'react-router-dom';
import MailMenu from '../../components/mail/mail-menu/MailMenu';
import MailContent from '../../components/mail/mail-content/MailContent';

function Mail() {
  const { section } = useParams();
  return (
    <S.MailContainer>
      <Row gutter={[4, 4]}>
        <Col xs={24} xl={5}>
          <MailMenu />
        </Col>
        <Col xs={24} xl={19}>
        <MailContent section={section}/>
        </Col>
      </Row>
    </S.MailContainer>
  );
}

export default Mail;
