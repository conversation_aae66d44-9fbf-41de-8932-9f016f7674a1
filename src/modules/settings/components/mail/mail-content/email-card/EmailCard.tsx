import React, { useState, useRef } from 'react';
import SettingBody from '../../../setting/body/SettingBody';
import Typography from '@/shared/components/common/Typography';
import AccessSwitch from '@/shared/components/common/AccessSwitch/AccessSwitch';
import * as S from './email-card.styles';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import themeColors from '@/shared/styles/themes/default/colors';
import { Skeleton } from 'antd';
import { Editor } from '@tinymce/tinymce-react';
import Button from '@/shared/components/common/Button';
import { useTranslation } from 'react-i18next';
import AutoSave from '../../../shared/autosave/AutoSave';

const emailOptions = [
  {
    key: 'transcript',
    labelKey: 'email-card.email-transcript',
  },
  {
    key: 'ratings',
    labelKey: 'email-card.enable-ratings',
  },
  {
    key: 'enrichProfiles',
    labelKey: 'email-card.enrich-profiles',
  },
  {
    key: 'spamFilter',
    labelKey: 'email-card.spam-filter',
  },
] as const;

type EmailOptionKey = (typeof emailOptions)[number]['key'];

const EmailCard = () => {
  const { t } = useTranslation('emailSetting');
  const [generalEnabled, setGeneralEnabled] = useState(false);
  const [signatureEnabled, setSignatureEnabled] = useState(false);
  const [editorReady, setEditorReady] = useState(false);
  const editorRef = useRef<any>(null);

  const [settings, setSettings] = useState<Record<EmailOptionKey, boolean>>({
    transcript: false,
    ratings: false,
    enrichProfiles: false,
    spamFilter: false,
  });

  const handleToggle = (key: EmailOptionKey) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <SettingBody
      title={t('email-card.title')}
      headerRight={<AutoSave/>
      }
    >
      {/* GENERAL OPTIONS */}
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('email-card.general-options-title')}
          </Typography>
          <AccessSwitch
            checked={generalEnabled}
            onChange={setGeneralEnabled}
            value="general-options"
          />
        </S.BoxTitle>

        {emailOptions.map(({ key, labelKey }) => (
          <S.BoxSubTitle key={key}>
            <Typography color={themeColors.neutral}>
              {t(labelKey)}
            </Typography>
            <AccessSwitch
              checked={settings[key]}
              onChange={() => handleToggle(key)}
              value={key}
            />
          </S.BoxSubTitle>
        ))}
      </S.BoxSection>

      {/* CUSTOM SIGNATURE */}
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('email-card.custom-signature-title')}
          </Typography>
        </S.BoxTitle>

        <S.BoxSubTitle>
          <Typography color={themeColors.neutral}>
            {t('email-card.custom-signature-description')}
          </Typography>
          <AccessSwitch
            checked={signatureEnabled}
            onChange={setSignatureEnabled}
            value="custom-signature"
          />
        </S.BoxSubTitle>

        {signatureEnabled && (
          <S.FormField>
            <Typography fontWeight={fontWeight.semiBold} margin="12px 0 12px 0">
              {t('email-card.signature-label')} <span style={{ color: 'red' }}>*</span>
            </Typography>

            {!editorReady && (
              <Skeleton
                active
                paragraph={{ rows: 12 }}
                title={false}
                style={{ height: 627, marginBottom: 24 }}
              />
            )}

            <div
              style={{
                display: editorReady ? 'block' : 'none',
                height: '50vh',
                maxHeight: '627px',
                width: '100%',
              }}
            >
              <Editor
                apiKey="10lpxjmyvyly4rdb88xil2fxm3y11ava3j2s5rn9tl5btib8"
                onInit={(evt, editor) => {
                  editorRef.current = editor;
                  setEditorReady(true);
                }}
                initialValue=""
                init={{
                  height: '100%',
                  menubar: false,
                  branding: false,
                  plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount',
                    'image media link',
                  ],
                  toolbar:
                    'undo redo | formatselect fontsizeselect | bold italic underline | link image media | ' +
                    'alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
                  fontsize_formats: '8pt 10pt 12pt 14pt 18pt 24pt 36pt',
                  file_picker_types: 'image media file',
                  file_picker_callback: (cb, value, meta) => {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');

                    if (meta.filetype === 'image') {
                      input.setAttribute('accept', 'image/*');
                    } else if (meta.filetype === 'media') {
                      input.setAttribute('accept', 'video/*');
                    } else {
                      input.setAttribute('accept', '*/*');
                    }

                    input.onchange = () => {
                      const file = input.files?.[0];
                      const reader = new FileReader();
                      reader.onload = () => {
                        cb(reader.result?.toString() || '', {
                          title: file?.name,
                        });
                      };
                      if (file) reader.readAsDataURL(file);
                    };

                    input.click();
                  },
                }}
              />
            </div>
            <S.ButtonWrapper>
              <Button type="primary">{t('email-card.save-button')}</Button>
            </S.ButtonWrapper>
          </S.FormField>
        )}
      </S.BoxSection>
    </SettingBody>
  );
};

export default EmailCard;
