import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/shared/components/common/Button';
import Modal from '@/shared/components/common/Modal';
import SettingBody from '@/modules/settings/components/setting/body/SettingBody';

import * as S from './ChatboxSecurity.styles';
import { ChatboxSecurityGlobalStyle } from './ChatboxSecurity.styles';

import icCheck from '@/assets/icons/setting/ic-check-circle.svg';
import checkIcon from '@/assets/icons/inbox/ic-check-radio.svg';
import closeIcon from '@/assets/icons/inbox/ic-close-radio.svg';
import AutoSave from '@/modules/settings/components/shared/autosave/AutoSave';

const ChatboxSecurity = () => {
  const { t } = useTranslation('chatbox');
  const [isPublic, setIsPublic] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenTotalPrivacy, setIsOpenTotalPrivacy] = useState(false);
  const [privacyIgnored, setPrivacyIgnored] = useState(false);
  const [totalPrivacyEnforced, setTotalPrivacyEnforced] = useState(false);

  return (
    <>
      <ChatboxSecurityGlobalStyle />
      <SettingBody
        title={t('chatbox.chatbox-security')}
        headerRight={<AutoSave/>}
      >
        <S.ChatboxBehaviorContent>
          <S.ChatboxBehaviorContentItem>
            <S.ChatboxBehaviorContentItemFooter>
              <p>Lock the chatbox to website domain (and subdomain)</p>

              <S.AccessRadioRow>
                <S.AccessSwitchWrapper>
                  <S.AccessSwitchInput
                    checked={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                  />
                  <S.AccessSwitchSlider checked={isPublic}>
                    <span className="switch-icon">
                      <img src={isPublic ? checkIcon : closeIcon} alt="" />
                    </span>
                  </S.AccessSwitchSlider>
                </S.AccessSwitchWrapper>
              </S.AccessRadioRow>
            </S.ChatboxBehaviorContentItemFooter>

            <S.ChatboxBehaviorContentItemFooter>
              <p>Ignore user privacy choices (MagicType, MagicBrowse)</p>

              <Button
                width="195px"
                type={privacyIgnored ? 'danger' : 'default'}
                className={privacyIgnored ? 'privacy-ignored-btn' : ''}
                onClick={() => {
                  if (!privacyIgnored) setIsOpen(true);
                }}
              >
                {privacyIgnored ? 'Privacy choices ignored' : 'Privacy choices honored'}
              </Button>
            </S.ChatboxBehaviorContentItemFooter>

            <S.ChatboxBehaviorContentItemFooter>
              <p>
                Do not auto-create chat sessions and cookies without user intent
              </p>

              <Button
                width="195px"
                type={totalPrivacyEnforced ? 'danger' : 'default'}
                className={totalPrivacyEnforced ? 'privacy-ignored-btn' : ''}
                onClick={() => {
                  if (!totalPrivacyEnforced) setIsOpenTotalPrivacy(true);
                }}
              >
                {totalPrivacyEnforced ? 'Total Privacy is enforced' : 'Total Privacy is inactive'}
              </Button>
            </S.ChatboxBehaviorContentItemFooter>
          </S.ChatboxBehaviorContentItem>
        </S.ChatboxBehaviorContent>

        <Modal
          width={700}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title={t('chatbox.enfore-total-privacy')}
          footer={
            <>
              <Button
                width="76px"
                type="default"
                onClick={() => setIsOpen(false)}
              >
                {t('chatbox.cancel')}
              </Button>

              <Button
                width="243px"
                type="danger"
                onClick={() => {
                  setIsOpen(false);
                  setPrivacyIgnored(true);
                }}
              >
                {t('chatbox.yes-degrade-zestchat-features')}
              </Button>
            </>
          }
        >
          <S.ChatboxSecurityContent>
            <p>{t('chatbox.if-you-organization-has-stringent')}</p>
          </S.ChatboxSecurityContent>
        </Modal>

        <Modal
          width={700}
          isOpen={isOpenTotalPrivacy}
          onClose={() => setIsOpenTotalPrivacy(false)}
          title={t('chatbox.ignore-user-privacy-choice')}
          footer={
            <>
              <Button
                width="160px"
                type="default"
                onClick={() => setIsOpen(false)}
              >
                {t('chatbox.i-respect-my-users')}
              </Button>

              <Button
                width="160px"
                type="danger"
                onClick={() => {
                  setIsOpenTotalPrivacy(false);
                  setTotalPrivacyEnforced(true);
                }}
              >
                {t('chatbox.lets-be-sneaky')}
              </Button>
            </>
          }
        >
          <S.ChatboxSecurityContent>
            <p>{t('chatbox.in-doing-this-you-agree-to-adjust')}</p>
          </S.ChatboxSecurityContent>
        </Modal>
      </SettingBody>
    </>
  );
};

export default ChatboxSecurity;
