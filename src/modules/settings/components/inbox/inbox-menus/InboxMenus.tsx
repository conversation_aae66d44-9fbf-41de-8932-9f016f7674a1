import React from 'react';
import { useTranslation } from 'react-i18next';

import SettingMenu from '@/modules/settings/components/setting/menu/SettingMenu';
import { InboxMenusEnums } from '@/modules/settings/helpers/enums/inbox';

import icInbox from '@/assets/icons/inbox/ic-inbox.svg';
import icSubInboxes from '@/assets/icons/inbox/ic-sub-inboxes.svg';
import icOperatorRouting from '@/assets/icons/inbox/ic-operator-routing.svg';
import icAutoTriage from '@/assets/icons/inbox/ic-auto-triage.svg';
import icMessageShortcuts from '@/assets/icons/inbox/ic-message-shortcuts.svg';

const INBOX_SETTING_MENU_LIST = [
  { key: InboxMenusEnums.CUSTOMIZE_YOUR_INBOX, label: 'customize', icon: icInbox },
  { key: InboxMenusEnums.SUB_INBOXES, label: 'subInboxes', icon: icSubInboxes },
  { key: InboxMenusEnums.OPERATOR_ROUTING, label: 'operatorRouting', icon: icOperatorRouting },
  { key: InboxMenusEnums.AUTO_TRIAGE, label: 'autoTriage', icon: icAutoTriage },
  { key: InboxMenusEnums.MESSAGE_SHORTCUTS, label: 'messageShortcuts', icon: icMessageShortcuts },
];

const InboxMenus: React.FC = () => {
  const { t } = useTranslation('inboxSetting');

  const menuItems = INBOX_SETTING_MENU_LIST.map((item) => ({
    key: item.key,
    label: t(`inbox-menu.${item.label}`),
    icon: item.icon, 
  }));

  return (
    <SettingMenu
      title={t('inbox-menu.title')}
      menuItems={menuItems}
      basePath="/setting/inbox"
    />
  );
};

export default InboxMenus;
