import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { Image } from 'antd';
import { ReactSVG } from 'react-svg';

import SettingBody from '@/modules/settings/components/setting/body/SettingBody';
import Button from '@/shared/components/common/Button';
import Typography from '@/shared/components/common/Typography';
import fontWeight from '@/shared/styles/themes/default/fontWeight';

import * as S from './plugin-detail.styles';

import icInstalled from '@/assets/icons/plugins/ic-install.svg';
import icConfigure from '@/assets/icons/plugins/ic-configure.svg';
import icInstall from '@/assets/icons/plugins/ic-add.svg';
import icGethelp from '@/assets/icons/plugins/ic-get-help.svg';
import icVideo from '@/assets/icons/plugins/ic-video.svg';
import icMessager from '@/assets/icons/plugins/ic-messager.svg';
import icContact from '@/assets/icons/plugins/ic-contact.svg';
import icGlobal from '@/assets/icons/plugins/ic-global.svg';

import { PluginsStatusEnums } from '../../helpers/enums/allPlugins';
import { CardPluginInterface } from '../../model/allPlugins';

interface PluginDetailsProps {
  selectedPlugin: CardPluginInterface | undefined;
}

const PluginDetails: FC<PluginDetailsProps> = ({ selectedPlugin }) => {
  const { t } = useTranslation('plugins');

  if (!selectedPlugin) {
    return <div>{t('plugins.notFound')}</div>;
  }

  return (
    <SettingBody
      headerRight={
        <S.WrapperHeader>
          <S.WrapperLeft>
            <Image
              preview={false}
              src={selectedPlugin.icon}
              width={90}
              height={90}
            />
            <S.ContainerWrapTitle>
              <S.WrapTitle>
                <Typography fontWeight={fontWeight.semiBold}>
                  {selectedPlugin.name}
                </Typography>
                <S.Type $type={selectedPlugin.type}>
                  <Typography textAlign="center">
                    {t(`plugins.${selectedPlugin.type}`)}
                  </Typography>
                </S.Type>
              </S.WrapTitle>
              <S.WrapTitle>
                <S.DeveloperBy>Developer by</S.DeveloperBy>
                <S.DeveloperName>Designful</S.DeveloperName>
              </S.WrapTitle>
            </S.ContainerWrapTitle>
          </S.WrapperLeft>
          <S.WrapperRight>
            {selectedPlugin.status === PluginsStatusEnums.UNINSTALLED ? (
              <Button type="primary" icon={<ReactSVG src={icInstall} />}>
                Install
              </Button>
            ) : (
              <>
                <Button icon={<ReactSVG src={icInstalled} />} disabled>
                  Installed
                </Button>
                <Button type="primary" icon={<ReactSVG src={icConfigure} />}>
                  Configure
                </Button>
              </>
            )}
            <Button icon={<ReactSVG src={icGethelp} />}>Get help</Button>
            <Button icon={<ReactSVG src={icVideo} />}>See video</Button>
          </S.WrapperRight>
        </S.WrapperHeader>
      }
    >
      <S.BoxSection>
        <S.SectionItem>
          <S.SectionTitle>
            <ReactSVG src={icMessager} />
            Messaging
          </S.SectionTitle>
          <Typography margin="0 0 0 32px">Read & write sessions</Typography>
          <Typography margin="0 0 0 32px">Read & write messages</Typography>
          <Typography margin="0 0 0 32px">Read pages</Typography>
          <Typography margin="0 0 0 32px">Read events</Typography>
          <Typography margin="0 0 0 32px">Write actions</Typography>
          <Typography margin="0 0 0 32px">Write routing</Typography>
        </S.SectionItem>
        <S.SectionItem>
          <S.SectionTitle>
            <ReactSVG src={icContact} />
            Contact
          </S.SectionTitle>
          <Typography margin="0 0 0 32px">Read profiles</Typography>
          <Typography margin="0 0 0 32px">Write events</Typography>
          <Typography margin="0 0 0 32px">Read data</Typography>
        </S.SectionItem>
        <S.SectionItem>
          <S.SectionTitle>
            <ReactSVG src={icGlobal} />
            Website
          </S.SectionTitle>
          <Typography margin="0 0 0 32px">Read availabillity</Typography>
          <Typography margin="0 0 0 32px">Read operators</Typography>
          <Typography margin="0 0 0 32px">Read verify settings</Typography>
          <Typography margin="0 0 0 32px">
            Read Knowledge Base information
          </Typography>
          <Typography margin="0 0 0 32px">
            Read Knowledge Base locales
          </Typography>
          <Typography margin="0 0 0 32px">
            Read Knowledges Base articles
          </Typography>
        </S.SectionItem>
      </S.BoxSection>
      <S.BoxSectionContent>
        <Typography>
          Help Desk Hero - AI-Powered Customer Support Analytics
        </Typography>

        <Typography>
          Turn your customer conversations into actionable business intelligence
          with Help Desk Hero, the AI-powered analytics tool that transforms
          your Crisp live chat interactions into valuable insights.
        </Typography>

        <Typography>🔍 Discover What Your Customers Really Think</Typography>
        <Typography>- Customer sentiment and satisfaction levels</Typography>
        <Typography>
          - Most requested features and product improvements
        </Typography>
        <Typography>- Common pain points and frustrations</Typography>
        <Typography>- Frequently asked questions and best answers</Typography>
        <Typography>- Emerging bugs and technical issues</Typography>

        <Typography>💡 Powerful Features</Typography>
        <Typography>- AI-generated conversation summaries</Typography>
        <Typography>- Topic clustering & intent detection</Typography>
        <Typography>- Sentiment analysis over time</Typography>
        <Typography>- Voice of customer dashboards</Typography>
        <Typography>- Team performance insights</Typography>

        <Typography>📊 Easy To Use</Typography>
        <Typography>- Connect your Crisp account</Typography>
        <Typography>- Analyze historic conversations instantly</Typography>
        <Typography>- Get weekly AI insights reports</Typography>

        <Typography>
          🛠 Built for Support Teams, Product Managers & Founders
        </Typography>
        <Typography>- Identify top feature requests</Typography>
        <Typography>- Track customer happiness over time</Typography>
        <Typography>- Discover what causes churn or conversion</Typography>
        <Typography>
          Help Desk Hero - AI-Powered Customer Support Analytics
        </Typography>

        <Typography>
          Turn your customer conversations into actionable business intelligence
          with Help Desk Hero, the AI-powered analytics tool that transforms
          your Crisp live chat interactions into valuable insights.
        </Typography>

        <Typography>🔍 Discover What Your Customers Really Think</Typography>
        <Typography>- Customer sentiment and satisfaction levels</Typography>
        <Typography>
          - Most requested features and product improvements
        </Typography>
        <Typography>- Common pain points and frustrations</Typography>
        <Typography>- Frequently asked questions and best answers</Typography>
        <Typography>- Emerging bugs and technical issues</Typography>

        <Typography>💡 Powerful Features</Typography>
        <Typography>- AI-generated conversation summaries</Typography>
        <Typography>- Topic clustering & intent detection</Typography>
        <Typography>- Sentiment analysis over time</Typography>
        <Typography>- Voice of customer dashboards</Typography>
        <Typography>- Team performance insights</Typography>

        <Typography>📊 Easy To Use</Typography>
        <Typography>- Connect your Crisp account</Typography>
        <Typography>- Analyze historic conversations instantly</Typography>
        <Typography>- Get weekly AI insights reports</Typography>

        <Typography>
          🛠 Built for Support Teams, Product Managers & Founders
        </Typography>
        <Typography>- Identify top feature requests</Typography>
        <Typography>- Track customer happiness over time</Typography>
        <Typography>- Discover what causes churn or conversion</Typography>
      </S.BoxSectionContent>
    </SettingBody>
  );
};

export default PluginDetails;
