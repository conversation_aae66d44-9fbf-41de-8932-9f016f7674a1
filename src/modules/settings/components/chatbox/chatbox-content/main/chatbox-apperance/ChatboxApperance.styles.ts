import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import { styled } from 'styled-components';

export const ChatboxBehaviorHeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${themeColors?.primary};

  p {
    font-size: 14px;
    font-weight: ${fontWeight?.medium};
    font-style: italic;
  }
`;

export const ChatboxBehaviorContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const ChatboxBehaviorContentItemHeader = styled.div`
  display: flex;
  padding: 5px 0px;
  padding-bottom: 10px;
  gap: 16px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
  transition: background-color 0.2s ease;
  h2 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  img {
    transition: transform 0.3s ease;
  }
`;

export const ChatboxBehaviorContentItemFooter = styled.div`
  display: flex;
  padding: 5px 0px;
  gap: 16px;
  justify-content: space-between;
  align-items: center;

  p {
    color: #333;
    font-size: 14px;
    font-weight: 400;
  }
`;

export const ChatboxApperanceContentItemFooter = styled.div`
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  color: #000000;
`;

export const ChatboxApperanceContentItemColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 15px;

  p {
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
    color: #000000;
  }
`;

export const ChatboxApperanceContentItemRow = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 4px;
  margin-top: 15px;
  align-items: center;

  p {
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: #000000;
  }

  span {
    font-size: 9px;
    line-height: 16px;
    font-weight: 400;
    color: #5b5b5b;
  }
`;

export const ChatboxApperanceColorRow = styled.div`
  display: flex;
  justify-content: space-evenly;
  margin-top: 10px;
  border-left: 1px solid #e8e8e8;
  padding-left: 10px;
  margin-left: 10px;
  align-items: center;

  p {
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: #000000;
  }

  span {
    font-size: 9px;
    line-height: 16px;
    font-weight: 400;
    color: #5b5b5b;
  }
`;

export const ChatboxApperanceRight = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
`;

export const ChatboxApperanceLeft = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
`;

export const ChatboxApperanceColumn = styled.div<{ $isActive?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  cursor: pointer;

  p {
    font-size: 14px;
    line-height: 20px;
    font-weight: ${({ $isActive }) => ($isActive ? '600' : '400')};
    color: ${({ $isActive }) => ($isActive ? themeColors?.primary : '#000000')};
    transition: all 0.2s ease;
  }

  &:hover {
    p {
      color: ${themeColors?.primary};
      font-weight: 600;
    }
  }
`;

export const ChatboxApperanceColorColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 35px;
  align-items: flex-start;

  p {
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: #333333;
  }
`;

export const ColorColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const InputWrapper = styled.div`
  display: flex;
  padding: 2px 8px;
  gap: 10px;
  align-items: center;
  border: 1px solid #e8e8e8;
  border-radius: 10px;

  input {
    width: 100%;
    border-radius: 10px;
    height: 40px;
    padding: 0 10px;
    border: none;
    outline: none;
  }
`;

export const BarWrapper = styled.div<{ $isActive?: boolean }>`
  width: 80px;
  height: 80px;
  border: 1px solid ${({ $isActive }) => ($isActive ? '#006124' : '#e8e8e8')};
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ $isActive }) =>
    $isActive ? '#e8f5e8' : 'transparent'};
  color: ${({ $isActive }) =>
    $isActive ? `${themeColors?.primary}` : '#8A8A8A'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border: 1px solid #006124;
    background-color: #e8f5e8;

    svg,
    img {
      color: ${themeColors?.primary};
    }
  }
`;

export const ChatboxBehaviorContentItem = styled.div`
  background-color: #fff;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #e8e8e8;

  display: flex;
  flex-direction: column;
  gap: 10px;
`;

// sub inbox

export const AccessRadioRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
`;

export const AccessSwitchWrapper = styled.label`
  position: relative;
  display: inline-block;
  width: 48px;
  height: 28px;
`;

export const AccessSwitchInput = styled.input.attrs({ type: 'checkbox' })`
  opacity: 0;
  width: 0;
  height: 0;
  &:checked + span {
    background: #006124;
  }
  &:checked + span:before {
    transform: translateX(20px);
  }
  &:disabled + span {
    background: #006124;
    cursor: not-allowed;
  }
  &:disabled + span:before {
    background: #006124;
  }
`;

export const AccessSwitchSlider = styled.span<{ checked?: boolean }>`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ checked }) => (checked ? '#005c27' : '#e6efea')};
  border-radius: 999px;
  transition: background 0.2s;
  box-shadow: ${(props) => props.theme.shadow.smallShadow};
  display: flex;
  align-items: center;
  justify-content: ${({ checked }) => (checked ? 'flex-end' : 'flex-start')};
  padding: 4px;

  .switch-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    box-shadow: ${(props) => props.theme.shadow.smallShadow};
    img {
      width: 14px;
      height: 14px;
    }
  }
`;

// More color setting UI
export const ColorSettingBlock = styled.div`
  background: #fff;
  border-left: 1px solid #e8e8e8;
  margin-top: 16px;
  padding: 24px 24px 16px 24px;
`;

export const ColorSettingSection = styled.div`
  margin-bottom: 16px;
`;

export const ColorSettingSectionTitle = styled.div`
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 12px;
`;

export const ColorSettingRow = styled.div`
  display: flex;
  flex-direction: row;
  gap: 24px;
  margin-bottom: 16px;
`;

export const ColorSettingCol = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 160px;
  gap: 6px;
`;

export const ColorInputWrap = styled.div`
  display: flex;
  align-items: center;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafbfc;
  padding: 0 8px;
  height: 36px;
  min-width: 120px;
`;

export const ColorTextInput = styled.input`
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: transparent;
  outline: none;
  font-size: 15px;
  width: 180px;
  height: 32px;
  padding: 20px 12px;
`;

export const ColorBox = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  margin: 0 6px;
`;

export const ColorDropdownIcon = styled.div`
  width: 18px;
  height: 18px;
  background: url('/src/assets/icons/setting/ic-arrow-up.svg') no-repeat center
    center;
  background-size: 14px 14px;
  transform: rotate(-90deg);
  opacity: 0.7;
`;

export const ColorSettingDivider = styled.div`
  border-bottom: 1px solid #e8e8e8;
  margin: 18px 0;
`;

export const PositionTitle = styled.div`
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 12px;
`;

export const PositionDivider = styled.div`
  border-bottom: 1px solid #e8e8e8;
  margin: 0 -24px 18px -24px;
`;

export const WidgetPositionTitle = styled.div`
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 12px;
`;

export const PositionRow = styled.div`
  display: flex;
  padding-left: 20px;
  border-left: 1px solid #e8e8e8;
  gap: 24px;
  margin-bottom: 18px;
`;

export const PositionCol = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

export const AlignSelect = styled.select`
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  outline: none;
  height: 36px;
  width: 120px;
  padding: 0 12px 0 12px;
  padding-right: 36px;
  font-size: 15px;
  background: #fff;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;

  &:focus {
    border-color: #9ca3af;
  }
`;

export const SpacingInput = styled.input`
  border-top: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  border-right: 1px solid transparent;
  border-left: 1px solid transparent;
  height: 36px;
  width: 60px;
  padding: 0 8px;
  font-size: 15px;
`;

export const PxText = styled.span`
  font-size: 14px;
  color: #888;
`;

export const VisibilityTitle = styled.div`
  display: flex;
  justify-content: space-between;
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 8px;
  margin-top: 8px;
`;

export const VisibilityDesc = styled.span`
  font-weight: 400;
  font-size: 12px;
  color: #888;
  margin-left: 8px;
`;

export const VisibilityOptions = styled.div`
  display: flex;
  padding-left: 20px;
  border-left: 1px solid #e8e8e8;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
`;

export const VisibilityMobileWidget = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
`;

export const VisibilityMobileWidgetRow = styled.div`
  display: flex;
  gap: 10px;
  align-items: center !important;
`;

export const VisibilityLabel = styled.label`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-weight: 500;
  width: 100%;
`;

export const VisibilityIcon = styled.img`
  width: 24px;
  height: 24px;
  margin-top: 2px;
  flex-shrink: 0;
  min-width: 24px;
  min-height: 24px;
`;

export const VisibilityRadio = styled.span`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1.5px solid #e8e8e8;
  display: inline-block;
  margin-top: 2px;
  background: #fff;
  flex-shrink: 0;
  min-width: 24px;
  min-height: 24px;
`;

export const PositionLabel = styled.label`
  font-size: 14px;
  margin-bottom: 4px;
`;

export const VisibilityTextBlock = styled.div`
  flex: 1;
  min-width: 0;
`;

export const VisibilityTextTitle = styled.div`
  font-weight: 400
  line-height: 20px;
  color: #333333;
  font-size: 14px;
`;

export const VisibilityTextDesc = styled.div`
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #5b5b5b;
`;

export const VisibilityRow = styled.div`
  display: flex;
  gap: 10px;
  align-items: center;
`;

export const ColorSettingLabel = styled.label`
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
`;

export const ColorSettingImg = styled.img`
  width: 200px;
  height: 36px;
  object-fit: contain;
`;

export const MobileWidgetSection = styled.div`
  margin-top: 16px;
  padding-left: 20px;
  border-left: 1px solid #e8e8e8;
`;

export const AppearanceSection = styled.div`
  margin-bottom: 16px;
`;

export const AppearanceTitle = styled.div`
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 8px;
  color: #333;
`;

export const MobileWidgetType = styled.div`
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
  color: #333;
`;

export const SpacingRow = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
`;

export const SpacingCol = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

export const MobileSpacingInputWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const MobileSpacingInput = styled.input`
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  height: 36px;
  width: 60px;
  padding: 0 8px;
  font-size: 15px;
  outline: none;
`;

export const SelectWrapper = styled.div`
  position: relative;
  width: 120px;
`;

export const ButonRelative = styled.div`
  position: relative;
  top: -5px;
`;

export const SelectArrowIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  pointer-events: none;
  background: url('/src/assets/icons/setting/ic-arrow-up.svg') no-repeat center
    center;
  background-size: 12px 12px;
  transform: translateY(-50%) rotate(180deg);
  opacity: 0.6;
`;

export const ColorCircleRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 50%;
`;

export const ColorCircle = styled.div<{ $color: string; $selected?: boolean }>`
  width: 20px;
  height: 20px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  border: ${({ $selected }) => ($selected ? '3px solid #1976D2' : '1px solid #fff')};
  background: ${({ $color }) => $color};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border 0.2s;
`;
