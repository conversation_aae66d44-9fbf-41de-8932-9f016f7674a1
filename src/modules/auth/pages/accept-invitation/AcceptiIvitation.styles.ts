import Select from '@/shared/components/common/Select';
import Typography from '@/shared/components/common/Typography';
import themeColors from '@/shared/styles/themes/default/colors';
import { Option } from 'antd/es/mentions';
import { styled } from 'styled-components';

export const SignInWrap = styled.section`
  display: flex;
  align-items: stretch;
  width: 100vw;
  height: 100vh;
  max-height: 100vh;

  @media (max-width: ${(props) =>
      props?.theme?.breakpoints?.smMax || '480px'}) {
    flex-direction: column;
    height: auto;
    margin: 0;
  }
`;

export const ImageSection = styled.div`
  position: relative;
  height: 100%;
  width: 40%;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    background-size: cover;
  }

  .ant-image {
    height: 100% !important;
    width: 100%;
  }

  .ant-image .ant-image-img {
    height: 100% !important;
  }

  @media (max-width: 768px) {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden;
  }
`;

export const MultipleLangWrap = styled.div`
  position: absolute;
  top: 36px;
  right: 36px;
  display: flex;
  align-items: center;

  @media (max-width: 480px) {
    bottom: 16px;
    right: 16px;
    flex-direction: column;
    align-items: flex-end;
  }
`;

export const ChangeLang = styled(Select)`
  min-width: 125px;
  width: fit-content;
  height: 40px !important;

  .ant-select-selector {
    border: 1px solid ${(props) => props?.theme?.colors?.neutral} !important;
    padding: 0 !important;
    min-height: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center;

    .ant-select-selection-item {
      display: flex !important;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;
      padding: 0 12px;
      width: 100%;
    }
  }

  .ant-select-arrow {
    margin-top: -4px;
  }
`;

export const LangOption = styled(Option)`
  display: flex;
  align-items: center;
  gap: 4px;
  height: 40px;
`;

export const WrapperBody = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
  padding: 32px;
  background-color: #fff;
  max-width: 500px;
  margin: 0 auto;

  @media (max-width: 480px) {
    padding: 24px 16px;
  }
`;

export const WrapperUser = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 10px;
  padding:8px 12px;
`;

export const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 9999px;
  overflow: hidden;

  .ant-image,
  .ant-image-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }
`;

export const WrapperDetail = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;

  > *:first-child {
    font-weight: 600;
    font-size: 16px;
  }

  > *:last-child {
    font-size: 14px;
  }
`;
