import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import { createGlobalStyle, styled } from 'styled-components';

export const ChatboxBehaviorHeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${themeColors?.primary};

  p {
    font-size: 14px;
    font-weight: ${fontWeight?.medium};
    font-style: italic;
  }
`;

export const ChatboxBehaviorContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const ChatboxBehaviorContentItemHeader = styled.div`
  display: flex;
  padding: 5px 0px;
  padding-bottom: 10px;
  gap: 16px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;

  h2 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }
`;

export const ChatboxBehaviorContentItemFooter = styled.div`
  display: flex;
  padding: 5px 0px;
  gap: 16px;
  justify-content: space-between;
  align-items: center;

  p {
    color: #333;
    font-size: 14px;
    font-weight: 400;
  }
`;

export const ChatboxBehaviorContentLabel = styled.label`
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 20px;

  span {
   color: red;
  }
`;

export const ChatboxBehaviorContentInput = styled.div`
  display: flex;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 600;
  background-color: #fff;
  align-items: center;
  border: 1px solid #E8E8E8;

  input {
    border: none;
    outline: none;
    font-size: 14px;
    background: transparent;
    flex: 1;
  }
`;

export const ChatboxBehaviorContentItem = styled.div`
  background-color: #fff;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #e8e8e8;

  display: flex;
  flex-direction: column;
  gap: 10px;
`;

// sub inbox

export const AccessRadioRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
`;

export const AccessSwitchWrapper = styled.label`
  position: relative;
  display: inline-block;
  width: 48px;
  height: 28px;
`;

export const AccessSwitchInput = styled.input.attrs({ type: 'checkbox' })`
  opacity: 0;
  width: 0;
  height: 0;
  &:checked + span {
    background: #006124;
  }
  &:checked + span:before {
    transform: translateX(20px);
  }
  &:disabled + span {
    background: #006124;
    cursor: not-allowed;
  }
  &:disabled + span:before {
    background: #006124;
  }
`;

export const AccessSwitchSlider = styled.span<{ checked?: boolean }>`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ checked }) => (checked ? '#005c27' : '#e6efea')};
  border-radius: 999px;
  transition: background 0.2s;
  box-shadow: ${(props) => props.theme.shadow.smallShadow};
  display: flex;
  align-items: center;
  justify-content: ${({ checked }) => (checked ? 'flex-end' : 'flex-start')};
  padding: 4px;

  .switch-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    box-shadow: ${(props) => props.theme.shadow.smallShadow};
    img {
      width: 14px;
      height: 14px;
    }
  }
`;

// Custom style for privacy ignored button
export const ChatboxSecurityGlobalStyle = createGlobalStyle`
  .privacy-ignored-btn {
    background: #fff !important;
    color: #D91F11 !important;
    border: 1.5px solid #D91F11 !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    box-shadow: none !important;
  }
`;

export const RestrictionListContainer = styled.div`
  width: 100%;
  padding-left: 16px;
  border-left: 1px solid #E8E8E8;
`;

export const RestrictionListHeader = styled.div`
  background: #e6efea;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  color: #006124;
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const RestrictionListRow = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
  background: #fff;
`;

export const RestrictionListText = styled.span`
  flex: 1;
  font-size: 15px;
`;

export const RestrictionTrashButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #D91F11;
  display: flex;
  align-items: center;
`;

export const RestrictionFlagIcon = styled.img`
  width: 24px;
  height: 16px;
  margin-right: 8px;
  border-radius: 3px;
  object-fit: cover;
`;

export const RestrictionListIcon = styled.img`
  width: 18px;
  margin-right: 8px;
`;
