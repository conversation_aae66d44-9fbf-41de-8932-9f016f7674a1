import { useState } from 'react';
import SettingBody from '../../../setting/body/SettingBody';
import { useTranslation } from 'react-i18next';
import * as S from './CustomizeKnowledgeBase.styles';
import Typography from '@/shared/components/common/Typography';
import fontWeight from '@/shared/styles/themes/default/fontWeight';
import themeColors from '@/shared/styles/themes/default/colors';
import Input from '@/shared/components/common/Input';
import icPaper from '@/assets/icons/knowledge-base/ic-paper2.svg';
import bgLogo from '@/assets/images/knowledge-base/headerlogo.png';
import { Image } from 'antd';
import Button from '@/shared/components/common/Button';
import AccessSwitch from '@/shared/components/common/AccessSwitch/AccessSwitch';
import AutoSave from '../../../shared/autosave/AutoSave';

const CustomizeKnowledgeBase = () => {
  const { t } = useTranslation('knowledgeBaseSetting');

  const emailOptions = [
    {
      key: 'mostRead',
      labelKey: 'Show a list of most frequently read articles on homepage',
    },
    {
      key: 'categoryImages',
      labelKey: 'Show category images on homepage',
    },
    {
      key: 'chatbox',
      labelKey: 'Display the ZestChat chatbox on the Knowledge Base',
    },
    {
      key: 'feedback',
      labelKey: 'Ask users for feedback at the end of the articles',
    },
    {
      key: 'localeSelector',
      labelKey: 'Show a locale selector in the header of the Knowledge Base',
    },
    {
      key: 'noIndex',
      labelKey: 'Forbid search engine indexing of all pages',
    },
    {
      key: 'statusAlert',
      labelKey:
        'Show an alert when status reports dead (if ZestChat Status is configured)',
    },
  ];

  const [settings, setSettings] = useState<Record<string, boolean>>({
    mostRead: true,
    categoryImages: false,
    chatbox: true,
    feedback: true,
    localeSelector: false,
    noIndex: false,
    statusAlert: true,
  });

  const handleToggle = (key: string) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <SettingBody
      title={t('knowledge-base.customize-knowledge-base')}
      headerRight={<AutoSave/>}
    >
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('setup-knowledge-base.general-options')}
          </Typography>
        </S.BoxTitle>
        <S.FormItem
          name="domain"
          rules={[
            {
              required: true,
              message: '',
            },
            {
              type: 'text',
              message: '',
            },
          ]}
        >
          <Input
            prefix={icPaper}
            label={t('setup-knowledge-base.basic-domain-label')}
            isRequired
            placeholder={t('setup-knowledge-base.basic-domain-placeholder')}
            type="text"
          />
        </S.FormItem>
        <S.BoxWrapField>
          <S.WrapLogo>
            <Typography fontWeight={fontWeight?.semiBold} padding="0 0 4px 0">
              Header logo
            </Typography>
            <S.HeaderLogo>
              <Image src={bgLogo} height={80} width={80} />
            </S.HeaderLogo>
          </S.WrapLogo>
          <S.WrapLogo>
            <Typography fontWeight={fontWeight?.semiBold} padding="0 0 4px 0">
              Footer logo
            </Typography>
            <S.FooterLogo>
              Select or drag file <Button>Browse file</Button>
            </S.FooterLogo>
          </S.WrapLogo>
        </S.BoxWrapField>
        <S.BoxWrapField>
          <S.WrapLogo>
            <Typography fontWeight={fontWeight?.semiBold} padding="0 0 4px 0">
              Banner
            </Typography>
            <S.FooterLogo>
              Select or drag file <Button>Browse file</Button>
            </S.FooterLogo>
          </S.WrapLogo>
        </S.BoxWrapField>
      </S.BoxSection>

      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            {t('setup-knowledge-base.custom-options')}
          </Typography>
        </S.BoxTitle>

        {emailOptions.map(({ key, labelKey }) => (
          <S.BoxSubTitle1 key={key}>
            <Typography color={themeColors.neutral}>{t(labelKey)}</Typography>
            <AccessSwitch
              checked={settings[key]}
              onChange={() => handleToggle(key)}
              value={key}
            />
          </S.BoxSubTitle1>
        ))}
        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            Show an alert when status reports dead (if ZestChat Status is
            configured)
          </Typography>
          <Button>Edit Included HTML Code</Button>
        </S.BoxSubTitle1>
      </S.BoxSection>
    </SettingBody>
  );
};

export default CustomizeKnowledgeBase;
