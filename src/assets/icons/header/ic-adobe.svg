<svg width="55" height="55" viewBox="0 0 55 55" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g filter="url(#filter0_d_1564_52601)">
<rect x="3" y="1" width="49" height="49" rx="4" fill="url(#pattern0_1564_52601)" shape-rendering="crispEdges"/>
<rect x="3.5" y="1.5" width="48" height="48" rx="3.5" stroke="#E8E8E8" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_1564_52601" x="0" y="0" width="55" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0 0.0784314 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1564_52601"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1564_52601" result="shape"/>
</filter>
<pattern id="pattern0_1564_52601" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_1564_52601" transform="scale(0.00195312)"/>
</pattern>
<image id="image0_1564_52601" width="512" height="512" preserveAspectRatio="none" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
