import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Image } from 'antd';

import SettingBody from '../../../setting/body/SettingBody';
import AutoSave from '../../../shared/autosave/AutoSave';
import Typography from '@/shared/components/common/Typography';
import AccessSwitch from '@/shared/components/common/AccessSwitch/AccessSwitch';
import Button from '@/shared/components/common/Button';

import themeColors from '@/shared/styles/themes/default/colors';
import fontWeight from '@/shared/styles/themes/default/fontWeight';

import bgEmpty from '@/assets/images/knowledge-base/empty-article.png';
import * as S from './OperatorRouting.styles';
import Modal from '@/shared/components/common/Modal';
import RoutingRuleModal from '../modal/RoutingRuleModal';

const OperatorRouting = () => {
  const { t } = useTranslation('inboxSetting');

  const [settings, setSettings] = useState<Record<string, boolean>>({
    'enable-general-options': true,
    'assign-on-response': true,
    'auto-assign-by-rule': false,
    'enable-common-rules': true,
    'reassign-after-day': false,
    'reassign-if-offline': true,
  });

  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleToggle = useCallback((key: string) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  }, []);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <SettingBody title={t('inbox-menu.customize')} headerRight={<AutoSave />}>
      {/* General Section */}
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            General options
          </Typography>
          <AccessSwitch
            checked={settings['enable-general-options']}
            onChange={() => handleToggle('enable-general-options')}
            value="enable-general-options"
          />
        </S.BoxTitle>

        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            Assign operator who responded upon response
          </Typography>
          <AccessSwitch
            checked={settings['assign-on-response']}
            onChange={() => handleToggle('assign-on-response')}
            value="assign-on-response"
          />
        </S.BoxSubTitle1>

        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            Auto-assign conversations according to configured rules
          </Typography>
          <AccessSwitch
            checked={settings['auto-assign-by-rule']}
            onChange={() => handleToggle('auto-assign-by-rule')}
            value="auto-assign-by-rule"
          />
        </S.BoxSubTitle1>
      </S.BoxSection>

      {/* Common Rules Section */}
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            Common rules
          </Typography>
          <AccessSwitch
            checked={settings['enable-common-rules']}
            onChange={() => handleToggle('enable-common-rules')}
            value="enable-common-rules"
          />
        </S.BoxTitle>

        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            Re-assign conversations a day after last operator message
          </Typography>
          <AccessSwitch
            checked={settings['reassign-after-day']}
            onChange={() => handleToggle('reassign-after-day')}
            value="reassign-after-day"
          />
        </S.BoxSubTitle1>

        <S.BoxSubTitle1>
          <Typography color={themeColors.neutral}>
            Re-assign conversations if assigned operator is offline
          </Typography>
          <AccessSwitch
            checked={settings['reassign-if-offline']}
            onChange={() => handleToggle('reassign-if-offline')}
            value="reassign-if-offline"
          />
        </S.BoxSubTitle1>
      </S.BoxSection>

      {/* Routing Rules Section */}
      <S.BoxSection>
        <S.BoxTitle>
          <Typography
            variant="h5"
            fontWeight={fontWeight.semiBold}
            color={themeColors.neutral}
          >
            Routing rules
          </Typography>
          <Button type="primary" onClick={openModal}>
            Add a routing rule
          </Button>
        </S.BoxTitle>

        <S.BoxRouting>
          <Image src={bgEmpty} preview={false} />
          <Typography fontWeight={fontWeight?.semiBold} variant="h5">
            You have no routing rule
          </Typography>
          <Typography margin="8px 0 0 0">
            Your routing rules will appear here.
          </Typography>
        </S.BoxRouting>
      </S.BoxSection>

     <RoutingRuleModal isOpen={isModalOpen} onClose={closeModal} />
    </SettingBody>
  );
};

export default OperatorRouting;
