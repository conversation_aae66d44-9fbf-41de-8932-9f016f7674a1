import { Skeleton } from 'antd';

import { PluginsTypeEnums } from '../../helpers/enums/allPlugins';

import { css, styled } from 'styled-components';
import themeColors from '@/shared/styles/themes/default/colors';

export const PluginsContainer = styled.section``;

export const WrapperHeader = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
`;

export const ContainerWrapTitle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const DeveloperBy = styled.div`
  font-weight: 400;
  font-size: 14px;
`;
export const DeveloperName = styled.div`
  font-weight: 400;
  font-size: 14px;
  text-decoration: underline;
  color: #186ade;
`;

export const WrapperLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const WrapperRight = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const WrapTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PluginsTypesContainer = styled.div`
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  flex-wrap: wrap;
`;

export const SkeletonContainer = styled(Skeleton.Button)``;

export const PluginType = styled.div<{ $type: PluginsTypeEnums }>`
  cursor: pointer;
  padding: 4px 12px;
  border-radius: 10px;
  background-color: #f6f6f6;
  border: 1px solid #8a8a8a;

  p,
  svg {
    color: #8a8a8a;
  }
  p {
    font-size: 14px !important;
  }
  ${({ $type }) => {
    switch ($type) {
      case PluginsTypeEnums?.ESSENTIALS:
        return css`
          &:hover {
            background-color: #e4f9d2;
            border: 1px solid #389e0d;

            p,
            svg {
              color: #389e0d;
            }
          }
        `;
      case PluginsTypeEnums?.MINI:
        return css`
          &:hover {
            background-color: #fde9e7;
            border: 1px solid #d91f11;

            p,
            svg {
              color: #d91f11;
            }
          }
        `;
      case PluginsTypeEnums?.FREE:
        return css`
          &:hover {
            background-color: #e8f0fd;
            border: 1px solid #186ade;

            p,
            svg {
              color: #186ade;
            }
          }
        `;
      case PluginsTypeEnums?.PLUS:
        return css`
          &:hover {
            background-color: #fef1d7;
            border: 1px solid #faad14;

            p,
            svg {
              color: #faad14;
            }
          }
        `;
      default:
        return css`
          &:hover {
            background-color: #feece6;
            border: 1px solid #fa541c;

            p,
            svg {
              color: #fa541c;
            }
          }
        `;
    }
  }}

  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
`;

export const Type = styled.div<{ $type: PluginsTypeEnums }>`
  cursor: pointer;
  padding: 2px 12px;
  border-radius: 10px;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;

  background-color: #f6f6f6;
  border: 1px solid #8a8a8a;

  p,
  svg {
    color: #8a8a8a;
  }

  p {
    font-size: 10px !important;
  }

  ${({ $type }) => {
    switch ($type) {
      case PluginsTypeEnums.ESSENTIALS:
        return css`
          background-color: #e4f9d2;
          border: 1px solid #389e0d;

          p,
          svg {
            color: #389e0d;
          }
        `;
      case PluginsTypeEnums.MINI:
        return css`
          background-color: #fde9e7;
          border: 1px solid #d91f11;

          p,
          svg {
            color: #d91f11;
          }
        `;
      case PluginsTypeEnums.FREE:
        return css`
          background-color: #e8f0fd;
          border: 1px solid #186ade;

          p,
          svg {
            color: #186ade;
          }
        `;
      case PluginsTypeEnums.PLUS:
        return css`
          background-color: #fef1d7;
          border: 1px solid #faad14;

          p,
          svg {
            color: #faad14;
          }
        `;
      default:
        return css`
          background-color: #feece6;
          border: 1px solid #fa541c;

          p,
          svg {
            color: #fa541c;
          }
        `;
    }
  }}
`;

export const BoxSection = styled.div`
  background-color: ${themeColors?.newtralLightest};
  border: 1px solid ${themeColors?.newtral};
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 12px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;

  @media ${({ theme }) => theme.breakpoints.mdMax} {
    grid-template-columns: repeat(1, 1fr);
  }

  @media ${({ theme }) => theme.breakpoints.lgMax} {
    grid-template-columns: repeat(2, 1fr);
  }
`;

export const SectionItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  p {
    color: #5b5b5b;
  }
`;

export const SectionTitle = styled.div`
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const BoxSectionContent = styled.div`
  background-color: ${themeColors?.newtralLightest};
  border: 1px solid ${themeColors?.newtral};
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 12px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;
