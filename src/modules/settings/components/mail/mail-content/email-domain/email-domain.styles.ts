import themeColors from '@/shared/styles/themes/default/colors';
import { Form } from 'antd';
import styled, { css } from 'styled-components';


export const BoxSection = styled.div`
  background-color: ${themeColors?.newtralLightest};
  border: 1px solid ${themeColors?.newtral};
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 12px;
`;

export const BoxTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${themeColors?.newtral};
  padding-bottom: 16px;
  margin-bottom: 16px;
`;
export const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: 16px;
  button {
    width: fit-content;
  }
`;

export const FormItem = styled(Form.Item)<{
  $margin?: string;
  $isRememberMe?: boolean;
}>`
  ${({ $margin }) =>
    $margin &&
    css`
      margin: ${$margin} !important;
    `}

  ${({ $isRememberMe }) =>
    $isRememberMe &&
    css`
      margin: 0 !important;

      .ant-form-item-control-input-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .ant-checkbox-label {
          display: none;
        }
      }
    `}
`;

export const BoxWrapField = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;

  > div {
    flex: 1;
  }
`;

export const Label = styled.span`
  position: absolute;
  top: 36px;
  right: 12px;
  font-weight: 400;
  font-size: 10px;
  z-index: 999;
  padding: 4px 12px;
  color: ${themeColors?.primary};
  background-color: ${themeColors?.primaryBackground};
  border-radius: 8px;
`;
